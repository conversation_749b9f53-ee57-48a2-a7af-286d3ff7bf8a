#!/usr/bin/env python
# encoding: utf-8

# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

fixed:
  type: doris
prompts:
  host:
    hint: 'hostname for your instance(your doris fe host)'
  port:
    default: 9030
    type: 'int'
    hint: 'port for your instance(your doris fe query_port)'
  schema:
    default: 'dbt'
    hint: 'the schema name as stored in the database,doris have not schema to make a collection of table or view'
  username:
    hint: 'your doris username'
  password:
    hint: 'your doris password, if no password, just Enter'
    hide_input: true
    default: ''
  threads:
    hint: "1 or more"
    type: "int"
    default: 1