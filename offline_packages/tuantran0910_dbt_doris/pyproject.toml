[project]
dynamic = ["version"]
name = "dbt-doris"
description = "The set of adapter protocols and base functionality that supports integration Doris with dbt-core"
readme = "README.md"
keywords = ["dbt", "adapter", "adapters", "database", "elt", "dbt-core", "dbt Core", "doris"]
requires-python = ">=3.10,<3.13"
authors = [
    { name = "long2ice,catpineapple,JNSimba", email = "<EMAIL>" },
    { name = "tuantran0910", email = "<EMAIL>" },
]
maintainers = [
    { name = "tuantran0910", email = "<EMAIL>" },
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: MacOS :: MacOS X",
    "Operating System :: Microsoft :: Windows",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "agate>=1.0,<2.0",
    "dbt-adapters>=1.14.3",
    "dbt-common>=1.15.0,<2.0",
    "dbt-core>=1.9.0",
    "mysql-connector-python>=8.0.0,<8.1",
]

[project.urls]
Homepage = "https://github.com/tuantran0910/dbt-doris"
Repository = "https://github.com/tuantran0910/dbt-doris.git"
Issues = "https://github.com/tuantran0910/dbt-doris/issues"
Changelog = "https://github.com/tuantran0910/dbt-doris/blob/main/CHANGELOG.md"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.sdist]
include = ["dbt"]

[tool.hatch.build.targets.wheel]
packages = ["dbt"]

[tool.hatch.version]
path = "dbt/adapters/doris/__version__.py"

[tool.ruff]
target-version = "py311"
line-length = 100
exclude = ["*/__init__.py"]
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["F"]
ignore = ["W", "F401", "F841"]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"
docstring-code-format = true
docstring-code-line-length = "dynamic"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

[tool.mypy]
ignore_missing_imports = true
follow_imports = "skip"
exclude = [
    "\\.eggs",
    "\\.git",
    "\\.mypy_cache",
    "\\.venv",
    "venv",
    "env",
    "venv.*",
    "_build",
    "build",
    "dist"
]

[tool.flake8]
ignore = [
    "W503",
    "E731",
    "E203", # black and flake8 disagree on whitespace before ':'
    "E501", # line too long (> 79 characters)
]

per-file-ignores = [
    "__init__.py:F401" # imported but unused
]

max-line-length = 100
max-complexity = 18

exclude = [
    ".venv/",
    "venv/"
]

[tool.pytest.ini_options]
testpaths = ["tests/integration", "tests/unit"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore:.*U.*mode is deprecated:DeprecationWarning",
    "ignore:.*'soft_unicode' has been renamed to 'soft_str'*:DeprecationWarning",
    "ignore:.*: ResourceWarning",
]

[dependency-groups]
dev = [
    "bumpversion>=0.6.0",
    "dbt-tests-adapter>=1.11.0",
    "flake8>=7.1.1",
    "pre-commit>=4.0.1",
    "pytest>=8.3.4",
    "pytest-dotenv>=0.5.2",
    "ruff>=0.8.3",
    "sqlfluff>=3.3.0",
    "dbt-tests-adapter>=1.11.0",
]
