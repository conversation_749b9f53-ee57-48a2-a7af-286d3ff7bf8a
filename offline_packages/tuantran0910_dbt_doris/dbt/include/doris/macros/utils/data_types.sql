{% macro doris__type_string() %}
    {{ return(api.Column.translate_type("string")) }}
{% endmacro %}


{% macro doris__type_timestamp() %}
    {{ return(api.Column.translate_type("datetime")) }}
{% endmacro %}


{% macro doris__type_float() %}
    {{ return(api.Column.translate_type("float")) }}
{% endmacro %}


{% macro doris__type_numeric() %}
    {{ return(api.Column.numeric_type("decimal", 28, 6)) }}
{% endmacro %}


{% macro doris__type_bigint() %}
    {{ return(api.Column.translate_type("bigint")) }}
{% endmacro %}


{% macro doris__type_int() %}
  {{ return(api.Column.translate_type("int")) }}
{% endmacro %}


{% macro doris__type_boolean() %}
  {{ return(api.Column.translate_type("boolean")) }}
{% endmacro %}
