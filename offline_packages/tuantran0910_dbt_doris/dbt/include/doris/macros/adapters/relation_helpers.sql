-- Licensed to the Apache Software Foundation (ASF) under one
-- or more contributor license agreements. See the NOTICE file
-- distributed with this work for additional information
-- regarding copyright ownership. The ASF licenses this file
-- to you under the Apache License, Version 2.0 (the
-- "License"); you may not use this file except in compliance
-- with the License. You may obtain a copy of the License at
--
-- http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing,
-- software distributed under the License is distributed on an
-- "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-- KIND, either express or implied. See the License for the
-- specific language governing permissions and limitations
-- under the License.

{% macro doris__olap_table(is_create_table_as) %}
    {% set is_create_table = is_create_table_as is none or not is_create_table_as %}

    {% set table_type = config.get('table_type', 'DUPLICATE') %}
    {% set keys = config.get('keys') %}
    {% set partition_by = config.get('partition_by') %}
    {% set partition_by_init = config.get('partition_by_init') %}
    {% set order_by = config.get('order_by') %}
    {% set partition_type = config.get('partition_type', 'RANGE') %}
    {% set buckets = config.get('buckets') %}
    {% set distributed_by = config.get('distributed_by') %}
    {% set properties = config.get('properties') %}
    {% set materialized = config.get('materialized', none) %}
    {% set unique_key = config.get('unique_key', none) %}

    {% if materialized == 'incremental' and (keys is not none or unique_key is not none) %}
        {% set table_type = 'UNIQUE' %}
        {% set keys = unique_key if unique_key is sequence and unique_key is not mapping and unique_key is not string else [unique_key] %}
    {% endif %}

    {% if properties is not none %}
        {% set replication_num = config.get('replication_num', 1) %}
        {% set replication_allocation =  config.get('replication_allocation') %}
        {% if replication_allocation is none %}
            {% do properties.update({'replication_num': replication_num}) %}
        {% else %}
            {% do properties.update({'replication_allocation': replication_allocation}) %}
        {% endif %}
    {% else %}
       {% set properties = {'replication_num': 1} %}
    {% endif %}

    {# 1. SET ENGINE #}
    {% if is_create_table %}
        ENGINE = OLAP
    {% endif %}

    {# 2. SET KEYS #}
    {% if keys is not none %}
        {% if table_type == "DUPLICATE" %}
            DUPLICATE KEY (
            {% for item in keys %}
                {{ item }} {% if not loop.last %}, {% endif %}
            {% endfor %}
            )
        {% elif table_type == "UNIQUE" %}
            UNIQUE KEY (
            {% for item in keys %}
                {{ item }} {% if not loop.last %}, {% endif %}
            {% endfor %}
            )
        {% else %}
            {% set msg %}
                "{{ table_type }}" is not support
            {% endset %}
            {{ exceptions.raise_compiler_error(msg) }}
        {% endif %}
    {% else %}
        {% if table_type != "DUPLICATE" %}
            {% set msg %}
                "{{ table_type }}" is must set "unique_key"
            {% endset %}
            {{ exceptions.raise_compiler_error(msg) }}
        {% endif %}
    {% endif %}

    {# 3. SET PARTITION #}
    {% if partition_by is not none %}
        {{ doris__partition_by(partition_type, partition_by) }}
    {% endif %}

    {# 4. SET DISTRIBUTED #}
    {% if distributed_by is not none %}
        DISTRIBUTED BY HASH (
        {% for item in distributed_by %}
            {{ item }} {% if not loop.last %}, {% endif %}
        {% endfor %}
        )
        {% if buckets is not none %}
            BUCKETS {{ buckets }}
        {% endif %}
    {% endif %}

    {# 5. SET ORDER BY #}
    {% if order_by is not none %}
        ORDER BY (
        {% for item in order_by %}
            {{ item }} {% if not loop.last %}, {% endif %}
        {% endfor %}
        )
    {% endif %}

    {# 6. SET PROPERTIES #}
    {% if properties is not none %}
        PROPERTIES (
        {% for key, value in properties.items() %}
            "{{ key }}" = "{{ value }}"
            {% if not loop.last %},
            {% endif %}
        {% endfor %}
        )
    {% endif %}
{% endmacro %}


{% macro doris__other_table() %}
    {% set engine = config.get('engine') %}
    {% set properties = config.get('properties') %}

    ENGINE = {{ engine }}
    {% if properties is not none %}
        PROPERTIES (
        {% for key, value in properties.items() %}
            "{{ key }}" = "{{ value }}"{% if not loop.last %},{% endif %}
        {% endfor %}
        )
    {% endif %}
{% endmacro %}


{% macro doris__partition_by(partition_type, partition_by) %}
    {% if partition_by is not none %}
        {% set partition_col = partition_by.get('field') %}
        {% set granularity = partition_by.get('granularity') %}
        {% if partition_col is none %}
            {% set msg = 'partition_col must be provided when using partition_by' %}
            {{ exceptions.raise_compiler_error(msg) }}
        {% endif %}

        {% if granularity is none %}
            {% set msg = 'granularity must be provided when using partition_by' %}
            {{ exceptions.raise_compiler_error(msg) }}
        {% endif %}
        AUTO PARTITION BY {{ partition_type }} (date_trunc({{ partition_col }}, '{{ granularity }}'))()
    {% endif %}
{% endmacro %}


{% macro doris__get_or_create_relation(database, schema, identifier, type) %}
    {% set target_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) %}

    {% if target_relation %}
        {% do return([true, target_relation]) %}
    {% endif %}

    {% set new_relation = api.Relation.create(
        database=none,
        schema=schema,
        identifier=identifier,
        type=type
    ) %}
    {% do return([false, new_relation]) %}
{% endmacro %}
