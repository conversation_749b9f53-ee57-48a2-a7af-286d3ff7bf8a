-- Licensed to the Apache Software Foundation (ASF) under one
-- or more contributor license agreements. See the NOTICE file
-- distributed with this work for additional information
-- regarding copyright ownership. The ASF licenses this file
-- to you under the Apache License, Version 2.0 (the
-- "License"); you may not use this file except in compliance
-- with the License. You may obtain a copy of the License at
--
-- http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing,
-- software distributed under the License is distributed on an
-- "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-- KIND, either express or implied. See the License for the
-- specific language governing permissions and limitations
-- under the License.

{% macro doris__drop_relation(relation) %}
    {% call statement('drop_relation', auto_begin=False) %}
        {% set relation_type = relation.type %}
        {% if not relation_type or relation_type is none %}
            {% set relation_type = 'table' %}
        {% endif %}

        {% if relation.is_materialized_view %}
            drop materialized view if exists {{ relation }};
        {% else %}
            drop {{ relation_type }} if exists {{ relation }};
        {% endif %}
    {% endcall %}
{% endmacro %}


{% macro doris__rename_relation(from_relation, to_relation) %}
    {% call statement('rename_relation') %}
        {% if from_relation.is_materialized_view and to_relation.is_materialized_view %}
            alter materialized view {{ from_relation }} rename {{ to_relation.table }}
        {% elif from_relation.is_table and to_relation.is_table %}
            alter table {{ from_relation }} rename {{ to_relation.table }}
        {% elif from_relation.is_view and to_relation.is_view %}
            {% set results = run_query("select VIEW_DEFINITION as sql from information_schema.views where TABLE_SCHEMA='"
                + from_relation.schema + "' and TABLE_NAME='" + from_relation.table + "'")
            %}
            create view {{ to_relation }} as {{ results[0]['sql'] }}
            {% call statement('drop_view') %}
                drop view if exists {{ from_relation }}
            {% endcall %}
        {% else %}
            {% set msg %}
                unsupported rename from {{ from_relation.type }} to {{ to_relation.type }}
            {% endset %}
            {{ exceptions.raise_compiler_error(msg) }}
        {% endif %}
    {% endcall %}
{% endmacro %}


{% macro doris__exchange_relation(first_relation, second_relation, is_drop_first=false) %}
    {% if second_relation.is_view %}
        {% set from_results = run_query('select VIEW_DEFINITION as sql from information_schema.views where TABLE_SCHEMA='"
            + first_relation.schema + "' and TABLE_NAME='" + first_relation.table + "'') %}
        {% set to_results = run_query('select VIEW_DEFINITION as sql from information_schema.views where TABLE_SCHEMA='"
            + second_relation.schema + "' and TABLE_NAME='" + second_relation.table + "'') %}

        {% call statement('exchange_view_relation') %}
            alter view {{ first_relation }} as {{ to_results[0]['sql'] }}
        {% endcall %}

        {% if is_drop_first %}
            {% do doris__drop_relation(second_relation) %}
        {% else %}
            {% call statement('exchange_view_relation') %}
                alter view {{ second_relation }} as {{ from_results[0]['sql'] }}
            {% endcall %}
        {% endif %}
    {% else %}
        {% call statement('exchange_relation') %}
            ALTER TABLE {{ first_relation }} REPLACE WITH TABLE `{{ second_relation.table }}` PROPERTIES('swap' = '{{ not is_drop_first }}');
        {% endcall %}
    {% endif %}
{% endmacro %}
