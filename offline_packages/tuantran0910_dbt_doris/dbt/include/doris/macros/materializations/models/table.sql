-- Licensed to the Apache Software Foundation (ASF) under one
-- or more contributor license agreements. See the NOTICE file
-- distributed with this work for additional information
-- regarding copyright ownership. The ASF licenses this file
-- to you under the Apache License, Version 2.0 (the
-- "License"); you may not use this file except in compliance
-- with the License. You may obtain a copy of the License at
--
-- http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing,
-- software distributed under the License is distributed on an
-- "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-- KIND, either express or implied. See the License for the
-- specific language governing permissions and limitations
-- under the License.

{% materialization table, adapter='doris' %}
    {% set existing_relation = load_cached_relation(this) %}
    {% set target_relation = this.incorporate(type='table') %}
    {% set intermediate_relation = make_intermediate_relation(target_relation) %}
    {% set preexisting_intermediate_relation = load_cached_relation(intermediate_relation) %}

    -- Grab current tables grants config for comparision later on
    {% set grant_config = config.get('grants') %}

    -- Drop the temp relations if they exist already in the database
    {{ doris__drop_relation(preexisting_intermediate_relation) }}

    -- build model
    {% call statement('main') %}
        {{ doris__create_table_as(False, intermediate_relation, sql) }}
    {% endcall %}

    {% if existing_relation %}
        {% if existing_relation.is_view %}
            {% do adapter.drop_relation(existing_relation) %}
            {% do adapter.rename_relation(intermediate_relation, target_relation) %}
        {% else %}
            {% do doris__exchange_relation(target_relation, intermediate_relation, True) %}
        {% endif %}
    {% else %}
        {{ adapter.rename_relation(intermediate_relation, target_relation) }}
    {% endif %}

    {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}
    {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}

    -- Alter relation comment
    {% do persist_docs(target_relation, model) %}

    -- Finally, drop the existing/backup relation after the commit
    {{ doris__drop_relation(intermediate_relation) }}

    {{ return({'relations': [target_relation]}) }}
{% endmaterialization %}


{% macro doris__create_table_as(temporary, relation, sql) %}
    {% set sql_header = config.get('sql_header', none) %}
    {% set engine = config.get('engine', 'OLAP') %}
    {% set indexs = config.get('indexs') %}
    {% set properties = config.get('properties') %}

    {{ sql_header if sql_header is not none }}

    create table {{ relation.include(database=False) }}
    {% if indexs is not none %}
        {% for index in indexs %}
            {% set columns = index.get('columns') %}
            (
                INDEX idx_{{ columns | replace(" ", "") | replace(",", "_") }} ({{ columns }}) USING BITMAP
            )
        {% endfor %}
    {% endif %}

    {% if engine == 'OLAP' %}
        {{ doris__olap_table(True) }}
    {% else %}
        {% set msg %}
            "ENGINE = {{ engine }}" does not support, currently only supports 'OLAP'
        {% endset %}
        {{ exceptions.raise_compiler_error(msg) }}
    {% endif %}

    as
    {{ sql }}
{% endmacro %}
