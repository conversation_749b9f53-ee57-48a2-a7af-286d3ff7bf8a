-- Licensed to the Apache Software Foundation (ASF) under one
-- or more contributor license agreements. See the NOTICE file
-- distributed with this work for additional information
-- regarding copyright ownership. The ASF licenses this file
-- to you under the Apache License, Version 2.0 (the
-- "License"); you may not use this file except in compliance
-- with the License. You may obtain a copy of the License at
--
-- http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing,
-- software distributed under the License is distributed on an
-- "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-- KIND, either express or implied. See the License for the
-- specific language governing permissions and limitations
-- under the License.

{% macro doris__get_replace_materialized_view_as_sql(relation, sql, existing_relation, backup_relation, intermediate_relation) %}
    {{ doris__get_drop_relation_sql(existing_relation) }}
    {{ doris__get_create_materialized_view_as_sql(relation, sql) }}
{% endmacro %}


{% macro doris__drop_materialized_view(relation) %}
    drop materialized view if exists {{ relation }};
{% endmacro %}


{% macro doris__get_create_materialized_view_as_sql(relation, sql) %}
    {% set partition_by = config.get('partition_by') %}
    {% set partition_type = config.get('partition_type', 'RANGE') %}
    {% set buckets = config.get('buckets') %}
    {% set distributed_by = config.get('distributed_by') %}
    {% set properties = config.get('properties') %}
    {% set refresh_method = config.get('refresh_method', 'auto') %}
    {% set refresh_trigger = config.get('refresh_trigger') %}

    create materialized view {{ relation }}

    {% if partition_by is not none %}
        {{ doris__partition_by(partition_type, partition_by) }}
    {% endif %}

    {% if distributed_by is not none %}
    DISTRIBUTED BY HASH (
        {% for item in distributed_by %}
            {{ item }} {% if not loop.last %}, {% endif %}
        {% endfor %}
    )
    {% endif %}

    {% if buckets is not none %}
        BUCKETS {{ buckets }}
    {% endif %}

    refresh {{ refresh_method }} {% if refresh_trigger is not none %}{{ refresh_trigger }}{% endif %}

    {% if properties is not none %}
        PROPERTIES (
        {% for key, value in properties.items() %}
            "{{ key }}" = "{{ value }}"{% if not loop.last %},{% endif %}
        {% endfor %}
        )
    {% endif %}

    as
    {{ sql }};
{% endmacro %}


{% macro doris__get_drop_relation_sql(relation) %}
    {% call statement(name="main") %}
        {% if relation.is_materialized_view %}
            {{ doris__drop_materialized_view(relation) }}
        {% else %}
            drop {{ relation.type }} if exists {{ relation }};
        {% endif %}
    {% endcall %}
{% endmacro %}


{% macro doris__get_materialized_view_configuration_changes(existing_relation, new_config) %}
{% endmacro %}


{% macro doris__get_alter_materialized_view_as_sql(
    relation,
    configuration_changes,
    sql,
    existing_relation,
    backup_relation,
    intermediate_relation
) %}
    {{ doris__get_replace_materialized_view_as_sql(relation, sql, existing_relation, backup_relation, intermediate_relation) }}
{% endmacro %}
