-- Licensed to the Apache Software Foundation (ASF) under one
-- or more contributor license agreements. See the NOTICE file
-- distributed with this work for additional information
-- regarding copyright ownership. The ASF licenses this file
-- to you under the Apache License, Version 2.0 (the
-- "License"); you may not use this file except in compliance
-- with the License. You may obtain a copy of the License at
--
-- http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing,
-- software distributed under the License is distributed on an
-- "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-- KIND, either express or implied. See the License for the
-- specific language governing permissions and limitations
-- under the License.

{% macro doris__validate_microbatch_config(config) %}
    {% set required_config_keys = ['event_time', 'begin', 'batch_size'] %}
    {% for key in required_config_keys %}
        {% if not config.get(key) %}
            {% do exceptions.raise_compiler_error("The 'microbatch' incremental strategy requires the '" ~ key ~ "' configuration to be set.") %}
        {% endif %}
    {% endfor %}
{% endmacro %}


{% macro doris__validate_get_incremental_strategy(config) %}
    {% set strategy = config.get('incremental_strategy') or 'append' %}
    {% set invalid_strategy_msg %}
        Invalid incremental strategy provided: {{ strategy }}
        Expected one of: 'append', 'merge', 'insert_overwrite', 'microbatch'
    {% endset %}
    {% if strategy not in ['append', 'merge', 'insert_overwrite', 'microbatch'] %}
        {% do exceptions.raise_compiler_error(invalid_strategy_msg) %}
    {% endif %}

    {% if strategy == 'microbatch' %}
        {% do doris__validate_microbatch_config(config) %}
    {% endif %}

    {% do return (strategy) %}
{% endmacro %}
