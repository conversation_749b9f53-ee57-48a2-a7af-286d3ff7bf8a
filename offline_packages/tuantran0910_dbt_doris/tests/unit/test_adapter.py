import dataclasses
from multiprocessing import get_context
from unittest import mock
from unittest import Test<PERSON>ase

import agate
from dbt_common.context import set_invocation_context
from dbt_common.exceptions import DbtValidationError

from dbt.adapters.base import BaseRelation
from dbt.adapters.contracts.relation import Path
from dbt.adapters.doris import DorisAdapter
from dbt.adapters.doris import Plugin as DorisPlugin
from tests.unit.utils import config_from_parts_or_dicts
from tests.unit.utils import inject_adapter
from tests.unit.utils import mock_connection


class TestDorisAdapter(TestCase):
    def setUp(self):
        project_cfg = {
            "name": "X",
            "version": "0.1",
            "profile": "test",
            "project-root": "/tmp/dbt/does-not-exist",
            "config-version": 2,
        }
        profile_cfg = {
            "outputs": {
                "test": {
                    "type": "doris",
                    "user": "root",
                    "host": "thishostshouldnotexist",
                    "pass": "",
                    "port": 9030,
                    "schema": "raw",
                }
            },
            "target": "test",
        }

        self.config = config_from_parts_or_dicts(project_cfg, profile_cfg)
        self.mp_context = get_context("spawn")
        self._adapter = None

    @property
    def adapter(self):
        if self._adapter is None:
            self._adapter = DorisAdapter(self.config, self.mp_context)
            inject_adapter(self._adapter, DorisPlugin)
        return self._adapter

    @mock.patch("dbt.adapters.doris.connections.mysql.connector")
    def test_acquire_connection_validations(self, connector):
        try:
            connection = self.adapter.acquire_connection("dummy")
        except DbtValidationError as e:
            self.fail("got DbtValidationError: {}".format(str(e)))
        except BaseException as e:
            self.fail("acquiring connection failed with unknown exception: {}".format(str(e)))
        self.assertEqual(connection.type, "doris")

        connector.connect.assert_not_called()
        connection.handle
        connector.connect.assert_called_once()

    @mock.patch("dbt.adapters.doris.connections.mysql.connector")
    def test_acquire_connection(self, connector):
        connection = self.adapter.acquire_connection("dummy")

        connector.connect.assert_not_called()
        connection.handle
        self.assertEqual(connection.state, "open")
        self.assertNotEqual(connection.handle, None)
        connector.connect.assert_called_once()

    def test_cancel_open_connections_empty(self):
        self.assertEqual(len(list(self.adapter.cancel_open_connections())), 0)

    def test_cancel_open_connections_master(self):
        key = self.adapter.connections.get_thread_identifier()
        self.adapter.connections.thread_connections[key] = mock_connection("master")
        self.assertEqual(len(list(self.adapter.cancel_open_connections())), 0)

    @mock.patch.object(DorisAdapter, "execute_macro")
    @mock.patch.object(DorisAdapter, "_get_catalog_relations")
    def test_get_catalog_various_schemas(self, mock_get_relations, mock_execute):
        self.catalog_test(mock_get_relations, mock_execute, False)

    @mock.patch.object(DorisAdapter, "execute_macro")
    @mock.patch.object(DorisAdapter, "_get_catalog_relations")
    def test_get_filtered_catalog(self, mock_get_relations, mock_execute):
        self.catalog_test(mock_get_relations, mock_execute, True)

    def catalog_test(self, mock_get_relations, mock_execute, filtered=False):
        column_names = ["table_database", "table_schema", "table_name"]
        # In Doris, schema == database, so both should be the same value
        relations = [
            BaseRelation(path=Path(schema="foo", identifier="bar")),
            BaseRelation(path=Path(schema="FOO", identifier="baz")),
            BaseRelation(path=Path(schema="dbt", identifier="bar")),
            BaseRelation(path=Path(schema="quux", identifier="bar")),
            BaseRelation(path=Path(schema="skip", identifier="bar")),
        ]
        rows = list(map(lambda x: dataclasses.astuple(x.path), relations))
        mock_execute.return_value = agate.Table(rows=rows, column_names=column_names)

        mock_get_relations.return_value = relations

        relation_configs = []
        # Update used_schemas to match the database=schema pattern
        used_schemas = {("foo", "foo"), ("quux", "quux")}

        set_invocation_context({})
        if filtered:
            catalog, exceptions = self.adapter.get_filtered_catalog(
                relation_configs, used_schemas, set([relations[0], relations[3]])
            )
        else:
            catalog, exceptions = self.adapter.get_catalog(relation_configs, used_schemas)

        # Convert catalog rows to sets of tuples for comparison
        tupled_catalog = {tuple(row) for row in catalog}
        if filtered:
            expected = {tuple(rows[0]), tuple(rows[3])}
            self.assertEqual(tupled_catalog, expected)
        else:
            expected = {tuple(rows[0]), tuple(rows[1]), tuple(rows[3])}
            self.assertEqual(tupled_catalog, expected)

        self.assertEqual(exceptions, [])
