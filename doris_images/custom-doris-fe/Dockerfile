FROM selectdb/doris.fe-ubuntu:3.0.3

COPY --chown=doris:doris jdbc /opt/apache-doris/fe/jdbc_drivers_temp
RUN mkdir -p /opt/apache-doris/fe/jdbc_drivers
RUN mv /opt/apache-doris/fe/jdbc_drivers_temp/* /opt/apache-doris/fe/jdbc_drivers
RUN rm -rf /opt/apache-doris/fe/jdbc_drivers_temp
RUN chmod -R 755 /opt/apache-doris/fe/jdbc_drivers

COPY --chown=doris:doris connectors /opt/apache-doris/fe/connectors_temp
RUN mkdir -p /opt/apache-doris/fe/connectors
RUN mv /opt/apache-doris/fe/connectors_temp/* /opt/apache-doris/fe/connectors
RUN rm -rf /opt/apache-doris/fe/connectors_temp
RUN chmod -R 755 /opt/apache-doris/fe/connectors
