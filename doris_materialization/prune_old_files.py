import os
import shutil
import datetime
from dagster import sensor, RunRequest, DagsterInstance, job, op, OpExecutionContext
from dagster_dbt import DbtCliResource


@op
def prune_old_files_op(context: OpExecutionContext):

    # clean old storage files older than 1 day
    instance = DagsterInstance.get()
    storage_dir = instance.storage_directory()

    if not storage_dir:
        context.log.info("storage directory not configured. skip pruning")
        return

    threshold_days = 1  # Keep files younger than 1 day
    threshold_time = datetime.datetime.now() - datetime.timedelta(days=threshold_days)

    deleted_count = 0
    for root, _, files in os.walk(storage_dir):
        for file in files:
            file_path = os.path.join(root, file)
            modified_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
            if modified_time < threshold_time:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    context.log.info(f"Deleted file: {file_path}")
                except OSError as e:
                    context.log.error(f"Error deleting file {file_path}: {e}")

    context.log.info(f"Deleted {deleted_count} old files.")

    # Optionally prune empty folders
    prune_empty_folders(storage_dir, context)


def prune_empty_folders(storage_dir, context):
    """Helper to prune empty folders within the storage directory."""

    deleted_count = 0
    for root, dirs, files in os.walk(storage_dir, topdown=False):  # Walk bottom-up
        for folder in dirs:
            folder_path = os.path.join(root, folder)
            if not os.listdir(folder_path):
                try:
                    os.rmdir(folder_path)
                    deleted_count += 1
                    context.log.info(f"Deleted empty folder: {folder_path}")
                except OSError as e:
                    context.log.error(f"Error deleting folder {folder_path}: {e}")

    context.log.info(f"Deleted {deleted_count} empty folders.")


@job
def prune_old_files_job():
    prune_old_files_op()


@sensor(job=prune_old_files_job, minimum_interval_seconds=3600)
def prune_old_files_sensor():
    """Sensor to trigger the file pruning job."""

    return RunRequest()

