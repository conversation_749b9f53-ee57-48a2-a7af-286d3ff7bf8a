from dagster import asset, OpExecutionContext, AssetKey
import pymysql
from doris_materialization.lib.config import doris_host, doris_port, doris_user, doris_password, analytics_database


@asset(
    compute_kind='python',
    deps=[AssetKey('latest_intraday_events_raw')],
    group_name="analytics"
)
def ga_init_intraday_events_snapshot(context: OpExecutionContext):

    # Connect to Doris
    context.log.info(f"Establishing connection to Doris host {doris_host}")
    conn = pymysql.connect(
        host=doris_host,
        port=doris_port,
        user=doris_user,
        password=doris_password,
        database=analytics_database
    )

    try:
        init_sql = f"""
        drop table if exists analytics.latest_intraday_events_snapshot;
        create table analytics.latest_intraday_events_snapshot
            (
                content_id                              bigint          not null,
                hash_id                                 bigint          not null,
                content_id_values                       string          null,
                ingest_timestamp                        datetime        not null,
                id                                      varchar(255)    null,
                client_id                               varchar(255)    not null,
                org_id                                  varchar(255)    null,
                ga4_id                                  bigint          null,
                export_date                             bigint          null,
                bq_source_table                         varchar(255)    null,
                measurement_id                          varchar(255)    null,
                shop_domain_name                        varchar(255)    null,
                event_date                              bigint          null,
                event_timestamp                         bigint          not null,
                created_date                            datetime(6)     not null,
                event_name                              string          null,
                page_location                           string          null,
                ga_session_id                           bigint          null,
                ga_session_number                       bigint          null,
                page_referrer                           string          null,
                user_params_source                      string          null,
                user_params_campaign                    string          null,
                user_params_medium                      string          null,
                term                                    string          null,
                batch_page_id                           bigint          null,
                page_title                              varchar(255)    null,
                session_engaged                         string          null,
                batch_ordering_id                       bigint          null,
                campaign                                string          null,
                source                                  string          null,
                medium                                  string          null,
                gclid                                   string          null,
                content                                 string          null,
                new_language                            string          null,
                exp_variant_string                      string          null,
                user_pseudo_id                          string          null,
                stream_id                               bigint          null,
                category                                string          null,
                mobile_brand_name                       string          null,
                mobile_model_name                       string          null,
                mobile_marketing_name                   string          null,
                mobile_os_hardware_model                string          null,
                operating_system                        string          null,
                operating_system_version                string          null,
                browser_version                         string          null,
                language                                string          null,
                hostname                                string          null,
                continent                               string          null,
                country                                 string          null,
                region                                  string          null,
                city                                    string          null,
                sub_continent                           string          null,
                metro                                   string          null,
                is_active_user                          tinyint         null,
                traffic_source_name                     string          null,
                traffic_source_medium                   string          null,
                traffic_source_source                   string          null,
                traffic_source_manual_campaign_id       string          null,
                traffic_source_manual_campaign_name     string          null,
                traffic_source_manual_source            string          null,
                traffic_source_manual_medium            string          null,
                traffic_source_manual_term              string          null,
                traffic_source_manual_content           string          null,
                traffic_source_manual_source_platform   string          null,
                traffic_source_manual_creative_format   string          null,
                traffic_source_manual_marketing_tactic  string          null,
                traffic_source_gclid                    string          null,
                traffic_source_dclid                    string          null,
                traffic_source_srsltid                  string          null,
                social_source_referral                  tinyint         null,
                ad_distribution_network                 tinyint         null,
                coreEventId                             tinyint         null,
                browser                                 string          null,
                app_load_id                             string          null,
                buyflow_start_id                        string          null
            )
            UNIQUE KEY(content_id)
            DISTRIBUTED BY HASH(content_id) BUCKETS 10
            PROPERTIES (
                "replication_num" = "1",
                "enable_unique_key_merge_on_write" = "true"
            );
"""
        context.log.debug(init_sql)
        context.log.info(f"Initializing new copy of `latest_intraday_events_snapshot`")
        with conn.cursor() as cursor:
            cursor.execute(init_sql)
        conn.commit()

    except Exception as err:
        context.log.error(err)

    return "latest_intraday_events_snapshot"
