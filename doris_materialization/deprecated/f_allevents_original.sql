-- create or replace view `{BQ_PROJECT}`.`{BQ_DATASET}`.{table_name} as
with
/*.
The core sub-routine reads the temp table that is created by the sid sub-routine
and joins it to the `{BQ_PROJECT}`.`{BQ_DATASET}`.{all_events} table
based on client_id and app_load_id. A new temp table is created by bigQuery that
contains the update core_event records and the client_event records.  The sessionId
and gcid that are NULL in the allEvent table for core_event records are updated from
the sid sub-routine. All remaining sub-routine and the final select statement will be
using the sessionId and gcid to do the joins and not the app_load_id.
*/
core as (
	select
		ae.clientId,
		ae.createDate as createDt,
		ae.session_id as initial_sessionId,
		ae.session_Id as sessionId,
		ae.app_load_id,
		ae.buyflow_start_id,
		ae.category,
		ae.city,
		ae.content,
		ae.country,
		ae.creative_name as creativeNm,
		ae.creative_Slot as creativeSlot,
		DefaultChannelGrouping,
		ae.event_name as eventNm,
		ae.gcid as initial_gcid,
		ae.gcid,
		ae.csm_gclid as gclid,
		ae.host,
		ae.id,
		ae.Language,
		ae.market,
		ae.metro,
		ae.mobile_os_Hardware_Model as mobile_osHardwareModel,
		ae.mobile_Brand_Name as mobileBrandNm,
		ae.mobile_marketing_name as mobileMarketingNm,
		ae.mobile_model_name as mobileModelNm,
		ae.offer_Set as offerSet,
		ae.operating_System as operatingSystem,
		ae.operating_System_Version as operatingSystemVersion,
		ae.order_Monthly_Total as orderMonthlyTotal,
		ae.order_Total as orderTotal,
		ae.orgid,
		ae.package_change_type as packageChangeTyp,
		ae.page_Location as pageLocation,
		ae.page_Referrer as pageReferrer,
		ae.page_Title as pageTitle,
		ae.page_Url as pageUrl,
		ae.promotion_Id as promotionId,
		ae.promotion_name as promotionNm,
		ae.session_number as sessionNbr,
		ae.shopperId,
		ae.shopper_Type as shopperTyp,
		ae.stream_Id as streamId,
		ae.continent,
		ae.sub_Continent as subContinent,
		ae.term,
		ae.orderid,
		ae.browser,
		ae.browser_Version as browserVersion,
		ae.ETLSOURCENM,
		ae.coreEventId,
		ae.traffic_Source_Campaign as trafficSourceCampaign,
		ae.traffic_Source_Medium as trafficSourceMedium,
		ae.traffic_Source_Source trafficSourceSource,
		case
			when ae.csm_gclid is null then COALESCE(ae.csm_campaign,'(not set)')
			when ae.csm_gclid is not null and ads.click_view_gclid is not null then ads.campaign_name
			else '(unknown)'
		end as Campaign,
		case
			when ae.csm_gclid is null then COALESCE(ae.csm_medium,'(none)')
			else 'cpc'
		end as Medium,
		case
			when ae.csm_gclid is null then COALESCE(ae.csm_source,'(direct)')
			else 'google'
		end as Source,
		ae.unique_plan_id
	from `atlas-datawarehouse-prd`.`stage`.all_events ae
	left join (
    	select
        	distinct client_id,
            click_view_gclid,
            campaign_name
        from `atlas-datawarehouse-prd`.`stage`.ads_campaign_stg
    ) ads on ae.clientId = ads.client_id and ae.csm_gclid =	ads.click_view_gclid
),
/*
The lndpg sub-routine reads the core temp table that is created by the core sub-routine
for all client_event records.  It obtain the information it needs based on a ranking of
the most popular values for traffic source, campaign, and medium values and the usr source, campaign, and medium values
and then it uses a COALESCE statment to get the first value that is not null for populating in the final select statement.
Traffic values are the preferred value but if it is NULL then the user values are used.
*/
lndpg as (
	select
    	distinct clientId,
        initial_SessionId,
        initial_gcid,
        LandingPage
    from (
		select
        	*,
            row_number() over(PARTITION BY clientId, initial_SessionId, initial_gcid ORDER BY createDt asc) as row_number
		from (
			select
            	distinct clientId,
                coalesce(initial_SessionId, SessionId) as initial_SessionId,
                coalesce(initial_gcid, gcid) as initial_gcid,
				createDt,
				case
					when pageLocation is null then NULL
					when strpos(pagelocation,'/') = 1 then pagelocation
					when strpos(substring(pagelocation,9),'?') = 0 then substring(pagelocation,9)
					else substring(pagelocation,9,strpos(substring(pagelocation,9),'?')-1)
				END AS LandingPage
			from core
        )
	)
	where row_number = 1
),
shpr as (
	select
    	distinct a.clientid,
        a.initial_SessionId,
        a.shopperId,
        a.initial_gcid,
        a.id,
		s.shopper_id,
		s.order_id,
        first_name,
		s.last_name,
		s.phone_number,
		s.email,
		s.Shopper_Type,
		s.Sales_Type,
		s.Install_option,
		s.address,
		s.city,
		s.state,
		s.zip,
		s.catalog_region,
		s.shopper_subtype,
		s.match_type,
		s.quality_score,
		s.quality_score_status,
		s.risk_score,
		s.risk_score_source,
		s.risk_score_details
	from core a
	join (
		select distinct
		    first_name,
            last_name,
            phone_number,
            email,
			client_id,
            shopper_Id,
            address,
            city,
            state,
            zip,
            catalog_region,
            shopper_subtype,
            shopper_type,
            sales_type,
            match_type,
            Install_option,
            order_id,
            quality_score,
            quality_score_status,
            risk_score,
            risk_score_source,
            risk_score_details
		from `atlas-datawarehouse-prd`.`stage`.shopper
	) s on a.clientId = s.client_id and a.shopperId = s.shopper_Id
	where a.shopperId is not null
),
/*
The cli sub-routine reads the core temp table that is created by the core sub-routine
for all client_event records.  Maximum value for the columns below are obtained and used
in the final select table to propagate values that are null in the core_event records.
*/
cli as (
	select
    	ae.clientId,
		initial_SessionId,
		initial_gcid,
		max(term) AS term,
		max(content) AS content,
		max(streamId) AS stream_id,
		max(category) AS category,
		max(mobileBrandNm) AS mobile_brand_name,
		max(mobileModelNm) AS mobile_model_name,
		max(mobileMarketingNm) AS mobile_marketing_name,
		max(mobile_osHardwareModel) AS mobile_os_hardware_model,
		max(operatingSystem) AS operating_system,
		max(operatingSystemVersion) AS operating_system_version,
		max(browserVersion) AS browser_version,
		max(`language`) AS `language`,
		max(host) AS hostname,
		max(continent) AS continent,
		max(country) AS country,
		max(metro) AS region,
		max(city) AS city,
		max(subContinent) AS sub_continent,
		max(metro) AS metro,
		max(browser) AS browser
	from core ae
	where ETLSOURCENM = 'client_events'
	and eventNm != 'buyflow_start'
	GROUP BY ae.clientId,initial_SessionId,initial_gcid
),
slst as (
	select
		source,
        category
    from `atlas-datawarehouse-prd`.`stage`.channelGroupSources
),
/*
The dfcg sub-routine reads the core temp table that is created by the core sub-routine
and the cms temp table created by the cms sub-routine.  It then evaluates several of the
columns created by the cms sub-routine and based on the defined criteria for determining
the DefaultChannelGrouping.  It is then identified as the source for DefaultChannelGrouping
in the final select statement.
The logic was based on the following link:
https://support.google.com/analytics/answer/9756891?hl=en
*/
dfcg as (
	select
    	clientId,
  		initial_gcid,
		initial_sessionId,
		id,
		max(DefaultChannelGrouping) as DefaultChannelGrouping
	from (
		select
			ae.clientId,
			ae.initial_gcid,
			ae.initial_SessionId,
			ae.id,
			case
				when regexp_contains(ae.medium, r'^(?i:e[-_ ]?mail)$') or regexp_contains(ae.source, r'^(?i:e[-_ ]?mail)$') then 'email'
				when regexp_contains(ae.medium, r'^(?i:Affiliates)$') then 'Affiliates'
				when regexp_contains(ae.medium, r'^(?i:referral|app|link)$') then 'Referral'
				when ae.source in (select source from slst where category = 'SOURCE_CATEGORY_SEARCH') and regexp_contains(ae.medium, r'^(?i:cpc|ppc|paidsearch)$') then 'Paid Search'
				when regexp_contains(ae.medium, r'^(?i:cpv|cpa|cpp|content-text)$') then 'Other Advertising'
				when regexp_contains(ae.medium, r'^(?i:expandable|interstitial|display|cpm|banner|)$') then 'Display'
				when ae.source in (select source from slst where category = 'SOURCE_CATEGORY_SOCIAL') and regexp_contains(ae.medium, r'^(?i:.*cp.*|ppc|retargeting|paid.*)$') then 'Paid Social'
				when (regexp_contains(ae.source, r'^(?i:\(direct\))$') and regexp_contains(ae.medium, r'^(?i:\((none|not set)\))$')) then 'Direct'
				when ae.source in (select source from slst where category = 'SOURCE_CATEGORY_SEARCH') or regexp_contains(ae.medium, r'^(?i:organic)$') then 'Organic Search'
				when ae.source in (select source from slst where category = 'SOURCE_CATEGORY_SOCIAL') or regexp_contains(ae.medium, r'^(?i:social|social-network|social-media|sm|social network|social media)$') then 'Organic Social'
				when ae.source in (select source from slst where category = 'SOURCE_CATEGORY_SHOPPING') or regexp_contains(ae.campaign, r'^(?i:.*(([^a-df-z]|^)shop|shopping).*)$') then 'Organic Shopping'
				else '(unavailable) or (other)'
			end as DefaultChannelGrouping,
		from core ae
	)
	where DefaultChannelGrouping != '(unavailable) or (other)'
  	GROUP BY clientId, initial_gcid, initial_sessionId, id
),
/*
The coev sub-routine reads the core temp table that is created by the core sub-routine
for core_event records only.  It then abtains the max value for three columns that will be
used to propogate to the client_event records that have NULL as their column values.
They are then identified as the source for these columns in the final select statement.
*/
-- get column values for columns that need to have data propagated to them from core events
coev as (
	select
    	clientId,
        initial_gcid,
        initial_sessionId,
        max(host) as hostname,
        max(shopperTyp) as COREShopperType,
        max(orgid) AS orgid
	from core
	where ETLSOURCENM = 'core_events'
	group by clientId,initial_gcid, initial_sessionId
),

core_buyflow_start_aggregate as (
  SELECT
    buyflow_start_id,
    ANY_VALUE(core_event_params_campaign) as core_buyflow_start_campaign,
    ANY_VALUE(core_event_params_source) as core_buyflow_start_source,
    ANY_VALUE(core_event_params_medium) as core_buyflow_start_medium,
  FROM `atlas-datawarehouse-prd`.`stage`.all_events
  GROUP BY buyflow_start_id
)

/*
This is the final select statement.  It pulls infromation from all the sub-routine's
except the sid sub-routine and creates the final view that the Looker will read.
It also changes the name of many of the columns to the business names that Looker is expecting.
*/
SELECT
	distinct ae.clientId AS	COREBrandID,
	ae.createDt AS RecordDATE,
	coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) as SessionID,
	ae.initial_sessionId as GASessionID,
	ae.app_load_id as CoreAppLoadID,
	ae.buyflow_start_id as CoreBuyflowStartID,
	COALESCE(ae.category, cli.category ) AS	MobileDeviceCategory,
	COALESCE(ae.city, cli.CITY) City,
	COALESCE(ae.content, cli.content) AS Content,
	COALESCE(ae.country, cli.country) AS Country,
	ae.creativeNm AS CreativeName,
	ae.creativeSlot	AS CreativeSlot,
	COALESCE(dfcg.DefaultChannelGrouping, 'Unassigned') AS DefaultChannelGrouping,
	ae.eventNm AS EventName,
	COALESCE(ae.gcid, ae.initial_gcid) AS GAUserID,
	case
    	when ae.host IS NULL OR ae.host = '' THEN coev.hostname
        ELSE ae.host
    END	AS Hostname,
	ae.id AS SurrogateKey,
	COALESCE(ae.Language, cli.`language`) AS Language,
	COALESCE(ae.market, cli.region)	AS Region,
	COALESCE(ae.metro, cli.metro) AS Metro,
	COALESCE(ae.mobile_osHardwareModel, cli.mobile_os_hardware_model) AS MobileDeviceInfo,
	COALESCE(ae.mobileBrandNm, cli.mobile_brand_name ) AS MobileDeviceBranding,
	COALESCE(ae.mobileMarketingNm, cli.mobile_marketing_name) AS MobileDeviceMarketingName,
	COALESCE(ae.mobileModelNm, cli.mobile_model_name) AS MobileDeviceModel,
	ae.offerSet AS COREOfferSet,
	COALESCE(ae.operatingSystem, cli.operating_system) AS OperatingSystem,
	COALESCE(ae.operatingSystemVersion, cli.operating_system_version) AS OperatingSystemVersion,
	ae.orderMonthlyTotal AS OrderMonthlyTotal,
	ae.orderTotal AS OrderTotal,
	COALESCE(ae.orgid, coev.orgid) AS COREOrganizationID,
	ae.packageChangeTyp	AS PackageChangeType,
	lndpg.landingPage,
	ae.pageLocation	AS Page,
	ae.pageReferrer AS PageReferralPath,
	ae.pageTitle AS PageTitle,
	ae.pageUrl AS PageFullURL,
	ae.promotionId AS PromotionID,
	ae.promotionNm AS PromotionName,
	ae.sessionNbr AS SessionNumber,
	ae.shopperId AS COREShopperID,
	-- COALESCE(sh.shopper_type, ae.shopperTyp, coev.COREShopperType) AS COREShopperType,
	COALESCE(ae.streamId, cli.stream_id) AS GAStreamID,
	COALESCE(ae.subContinent, cli.sub_continent) AS Subcontinent,
	COALESCE(ae.term, cli.term) AS Term,
 	ae.Campaign as Campaign,
	ae.Medium as Medium,
	ae.Source as Source,
	ae.orderid as TransactionId,
	ord.order_status,
	ord.ticket_type,
	ord.ticket_status,
	ord.reason,
	sh.match_type,
	ord.upsell_billing_total as UpsellBillingTotal,
	ord.upsell_video as UpsellVideo,
	ord.upsell_phone as UpsellPhone,
	ord.upsell_package as UpsellPackage,
	ord.order_package as OrderPackage,
	ord.billing_account_number as BillingAccountNumber,
	ord.address_external_id as AddressExternalId,
	sh.Shopper_Type as ShopperType,
	sh.Sales_Type as SalesType,
	sh.Install_option as InstallOption,
    sh.first_name as ShopperFirstName,
    sh.last_name as ShopperLastName,
    sh.phone_number as ShopperPhoneNumber,
    sh.email as ShopperEmail,
	sh.address as CoreAddress,
	sh.city as CoreCity,
	sh.state as CoreState,
	sh.zip as CoreZip,
	sh.catalog_region as Catalogregion,
	sh.shopper_subtype as ShopperSubtype,
	sh.quality_score as QualityScore,
	sh.quality_score_status as QualityScoreStatus,
	sh.risk_score as RiskScore,
	sh.risk_score_source as RiskScoreSource,
	sh.risk_score_details as RiskScoreDetails,
	COALESCE(ae.browser, cli.browser) AS Browser,
	COALESCE(ae.browserVersion ,cli.browser_version ) AS BrowserVersion,
	EXTRACT(HOUR FROM ae.createDt AT TIME ZONE "America/Chicago") AS Hour,
	CASE
		WHEN EXTRACT(DAYOFWEEK FROM ae.createDt AT TIME ZONE "America/Chicago") = 1 THEN 'Sunday'
		WHEN EXTRACT(DAYOFWEEK FROM ae.createDt AT TIME ZONE "America/Chicago") = 2 THEN 'Monday'
		WHEN EXTRACT(DAYOFWEEK FROM ae.createDt AT TIME ZONE "America/Chicago") = 3 THEN 'Tuesday'
		WHEN EXTRACT(DAYOFWEEK FROM ae.createDt AT TIME ZONE "America/Chicago") = 4 THEN 'Wednesday'
		WHEN EXTRACT(DAYOFWEEK FROM ae.createDt AT TIME ZONE "America/Chicago") = 5 THEN 'Thursday'
		WHEN EXTRACT(DAYOFWEEK FROM ae.createDt AT TIME ZONE "America/Chicago") = 6 THEN 'Friday'
		WHEN EXTRACT(DAYOFWEEK FROM ae.createDt AT TIME ZONE "America/Chicago") = 7 THEN 'Saturday'
	END AS DayofWeek,
	case when ae.eventNm = 'first_visit' then 'New' else 'Returning' end AS UserType,

/*
These final case statements are used to create the metrics in Looker.  For the ones that have a
sessionId or gcid as the returned results; Looker will do a count(distinct) to get the sum of
each of them for reporting.  For the ones with a 1 as the return result; Looker will just sum them
for reporting.
*/

	CASE WHEN ae.eventNm = 'first_visit' then ae.gcid ELSE NULL END AS FirstVisitUserID,
	CASE WHEN ae.eventNm = "view_item_list" THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) end AS ViewItemListSessionID,
	CASE WHEN CAST(ae.packageChangeTyp AS STRING) = 'initial add' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS SessInitialAddSessionID,
	CASE WHEN ae.eventNm = 'view_item_list' THEN COALESCE(ae.gcid, cli.initial_gcid) ELSE NULL END AS ViewItemListUserid,
	CASE WHEN ae.orderid IS NOT NULL THEN 1 ELSE NULL END AS TransactionMetric,
	CASE WHEN ae.eventNm = "refund" THEN 1 ELSE NULL END AS RefundMetric,
	CASE WHEN ae.eventNm = "address_entered" THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) ELSE NULL END AS AddressEnteredSessionId,
	CASE WHEN ae.eventNm = "add_to_cart" THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) ELSE NULL END AS CartsCreatedSessionId,
	CASE WHEN ae.eventNm = "view_item_list" THEN 1 ELSE NULL END AS ItemListViewMetric,
	CASE WHEN ae.eventNm = "add_to_cart" THEN 1 ELSE NULL END AS ItemAddtoCartMetric,
	CASE WHEN ae.eventNm = "view_item" THEN 1 ELSE NULL END AS ItemViewMetric,
	CASE WHEN ae.eventNm = "purchase" THEN 1 ELSE NULL END AS ItemPurchaseMetric,
	CASE WHEN ae.eventNm = "begin_checkout" THEN 1 ELSE NULL END AS ItemBeginCheckoutMetric,
	CASE WHEN ae.eventNm = "remove_from_cart" THEN 1 ELSE NULL END AS ItemRemovefromCartMetric,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%creditCheck%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS CreditCheckSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%activeAccount%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS ActiveAccountSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%prepayment%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS PrepaymentSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%presales%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS PresalesSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%chooseServices%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS ChooseServicesSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%presaleError%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS PresaleErrorSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%setupAccount%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS SetupAccountSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%daddressToolbar%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS AddressToolbarSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%orderConfirmation%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS OrderConfirmationSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%checkAddress%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS CheckAddressSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%error%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS ErrorSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%presaleConfirmation%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id)  END AS PresaleConfirmationSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%noservice%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS NoserviceSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%scheduleInstallation%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS ScheduleInstallationSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%comingSoon%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS ComingSoonSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%leadCapture%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS LeadCaptureSessionId,
	CASE WHEN ae.eventNm = "page_view" and ae.pageLocation LIKE '%customizeOptions%' THEN coalesce(concat(ae.initial_sessionId, ae.initial_gcid), ae.app_load_id) END AS CustomizeOptionsSessionId,
	ae.ETLSOURCENM	AS	ETLSourceName,
	ae.coreEventId,
    ae.gclid,
    cbsa.core_buyflow_start_campaign,
    cbsa.core_buyflow_start_source,
    cbsa.core_buyflow_start_medium,
    ae.unique_plan_id
FROM core ae
left join cli on ae.clientid = cli.clientid
			and ae.initial_gcid = cli.initial_gcid
			and ae.initial_sessionId = cli.initial_sessionId
left join coev on ae.clientid = coev.clientid
			and ae.initial_gcid = coev.initial_gcid
			and ae.initial_sessionId = coev.initial_sessionId
left join dfcg on ae.id = dfcg.id
left join lndpg on ae.clientid = lndpg.clientid
			and ae.initial_gcid = lndpg.initial_gcid
			and ae.initial_sessionId = lndpg.initial_sessionId
left join (
	select
		distinct client_id,
        order_id,
        order_status,
        ticket_type,
        ticket_status,
        reason,
        match_type,
        upsell_billing_total,
        upsell_video,
        upsell_phone,
		upsell_package,
        Shopper_Type,
        Sales_Type,
        Install_option,
        order_package,
        billing_account_number,
        address_external_id
	from `atlas-datawarehouse-prd`.`stage`.order
) ord on ae.clientId = ord.client_id and ae.orderid = ord.order_id
left join shpr sh on ae.clientId = sh.clientid and ae.id = sh.id
left join core_buyflow_start_aggregate cbsa on ae.buyflow_start_id = cbsa.buyflow_start_id

-- test filters!  Not part of the final materialization
where ord.address_external_id is not null
limit 100