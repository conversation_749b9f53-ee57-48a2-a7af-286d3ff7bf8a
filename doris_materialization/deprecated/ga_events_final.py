from dagster_dbt import dbt_assets, DbtCliResource, DbtProject, DagsterDbtTranslator, DagsterDbtTranslatorSettings
import dagster as dg
import json
from doris_materialization.lib.config import dbt_path
from doris_materialization.lib.ga_ingest_config import GoogleAnalyticsIngestConfig

dbt_project = DbtProject(project_dir=dbt_path)
dbt_project.prepare_if_dev()

def ga_events_final(config: GoogleAnalyticsIngestConfig)-> dg.AssetsDefinition:
    iteration_label = ""
    iteration = config.vars.get("iteration")
    if iteration:
        iteration_label = str(iteration)
    asset_name = f"ga_events_final_{iteration_label}"

    class FactoryDbtAssetNameTranslator(DagsterDbtTranslator):
        settings = DagsterDbtTranslatorSettings(enable_code_references=True)

        def get_asset_key(self, dbt_resource_props) -> dg.AssetKey:
            original_asset_key = super().get_asset_key(dbt_resource_props)
            modified_key = original_asset_key.path[:-1] + [original_asset_key.path[-1] + '_' + iteration_label]
            return dg.AssetKey(modified_key)

    @dbt_assets(
        name=asset_name,
        manifest=dbt_project.manifest_path,
        dagster_dbt_translator = FactoryDbtAssetNameTranslator(),
        project = dbt_project,
        pool = 'ga_events_final',
    )
    def _ga_events_final(context: dg.AssetExecutionContext, dbt: DbtCliResource):
        dg.get_dagster_logger().info(f"Running dbt with target: {config.target}, vars: {config.vars}")

        # define the command line arguments to pass to dbt
        dbt_args = ["build", "--target", config.target]

        # pull the variables dict from the config object and format for dbt command line
        vars_json = json.dumps(config.vars)
        dbt_args.extend(["--vars", f"{vars_json}"])

        yield from dbt.cli(dbt_args, context=context).stream()

    return _ga_events_final

