import dagster as dg
import pymysql
from doris_materialization.lib.config import doris_host, doris_port, doris_user, doris_password, analytics_database
from datetime import datetime

class GAEventsConfig(dg.Config):
    reload_days: int = None
    source_batch_size: int = 15


@dg.asset(
    compute_kind='python',
    deps=[dg.Asset<PERSON>ey('ga_events_dated_final')],
    group_name="analytics",
    description="Aggregates all intraday GA events tables into a single partitioned table, keeping only the most recent version of each event record based on hash_id and ingest_timestamp"
)
def ga_events_base(context: dg.OpExecutionContext, config: GAEventsConfig):

    # Connect to Doris
    context.log.info(f"Establishing connection to Doris host {doris_host}")
    conn = pymysql.connect(
        host=doris_host,
        port=doris_port,
        user=doris_user,
        password=doris_password,
        database=analytics_database
    )

    target_present_sql = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'analytics'
          AND table_name = 'ga_events_base'
    """

    with conn.cursor() as cursor:
        cursor.execute(target_present_sql)
        target_table_list = [row[0] for row in cursor.fetchall()]
    conn.commit()

    target_table_exists = True
    if len(target_table_list) == 0:
        target_table_exists = False
        context.log.info(f"Target table `analytics.ga_events_base` does not exist, full materialization required")
    else:
        context.log.info(f"Table `analytics.ga_events_base` exists, incremental materialization is possible")

    if config.reload_days is None or target_table_exists is False:

        # drop the existing target table so we can build from scratch
        drop_table_sql = """
            drop table if exists analytics.ga_events_base;
       """

        # generate a new target table
        init_table_sql = """
            create table analytics.ga_events_base
                (
                    content_id                              bigint          not null,
                    created_date                            datetime(6)     not null,
                    client_id                               varchar(255)    not null,
                    hash_id                                 bigint          not null,
                    content_id_values                       string          null,
                    ingest_timestamp                        datetime        not null,
                    id                                      varchar(255)    null,
                    org_id                                  varchar(255)    null,
                    ga4_id                                  bigint          null,
                    export_date                             bigint          null,
                    bq_source_table                         varchar(255)    null,
                    measurement_id                          varchar(255)    null,
                    shop_domain_name                        varchar(255)    null,
                    event_date                              bigint          null,
                    event_timestamp                         bigint          not null,
                    event_name                              string          null,
                    page_location                           string          null,
                    ga_session_id                           bigint          null,
                    ga_session_number                       bigint          null,
                    page_referrer                           string          null,
                    user_params_source                      string          null,
                    user_params_campaign                    string          null,
                    user_params_medium                      string          null,
                    term                                    string          null,
                    batch_page_id                           bigint          null,
                    page_title                              varchar(255)    null,
                    session_engaged                         string          null,
                    batch_ordering_id                       bigint          null,
                    -- campaign                                string          null,
                    -- source                                  string          null,
                    -- medium                                  string          null,
                    gclid                                   string          null,
                    content                                 string          null,
                    new_language                            string          null,
                    exp_variant_string                      string          null,
                    user_pseudo_id                          string          null,
                    stream_id                               bigint          null,
                    category                                string          null,
                    mobile_brand_name                       string          null,
                    mobile_model_name                       string          null,
                    mobile_marketing_name                   string          null,
                    mobile_os_hardware_model                string          null,
                    operating_system                        string          null,
                    operating_system_version                string          null,
                    browser_version                         string          null,
                    language                                string          null,
                    hostname                                string          null,
                    continent                               string          null,
                    country                                 string          null,
                    region                                  string          null,
                    city                                    string          null,
                    sub_continent                           string          null,
                    metro                                   string          null,
                    is_active_user                          tinyint         null,
                    traffic_source_name                     string          null,
                    traffic_source_medium                   string          null,
                    traffic_source_source                   string          null,
                    traffic_source_manual_campaign_id       string          null,
                    traffic_source_manual_campaign_name     string          null,
                    traffic_source_manual_source            string          null,
                    traffic_source_manual_medium            string          null,
                    traffic_source_manual_term              string          null,
                    traffic_source_manual_content           string          null,
                    traffic_source_manual_source_platform   string          null,
                    traffic_source_manual_creative_format   string          null,
                    traffic_source_manual_marketing_tactic  string          null,
                    traffic_source_gclid                    string          null,
                    traffic_source_dclid                    string          null,
                    traffic_source_srsltid                  string          null,
                    social_source_referral                  tinyint         null,
                    ad_distribution_network                 tinyint         null,
                    coreEventId                             tinyint         null,
                    browser                                 string          null,
                    app_load_id                             string          null,
                    buyflow_start_id                        string          null
                )
                ENGINE=OLAP
                UNIQUE KEY(content_id,created_date,client_id)
                AUTO PARTITION BY RANGE (DATE_TRUNC(created_date, 'month'))
                (
                PARTITION p202306 VALUES LESS THAN ('2023-06-01 00:00:00'),
                PARTITION p202307 VALUES LESS THAN ('2023-07-01 00:00:00'),
                PARTITION p202308 VALUES LESS THAN ('2023-08-01 00:00:00'),
                PARTITION p202309 VALUES LESS THAN ('2023-09-01 00:00:00'),
                PARTITION p202310 VALUES LESS THAN ('2023-10-01 00:00:00'),
                PARTITION p202311 VALUES LESS THAN ('2023-11-01 00:00:00'),
                PARTITION p202312 VALUES LESS THAN ('2023-12-01 00:00:00'),
                PARTITION p202401 VALUES LESS THAN ('2024-01-01 00:00:00'),
                PARTITION p202402 VALUES LESS THAN ('2024-02-01 00:00:00'),
                PARTITION p202403 VALUES LESS THAN ('2024-03-01 00:00:00'),
                PARTITION p202404 VALUES LESS THAN ('2024-04-01 00:00:00'),
                PARTITION p202405 VALUES LESS THAN ('2024-05-01 00:00:00'),
                PARTITION p202406 VALUES LESS THAN ('2024-06-01 00:00:00'),
                PARTITION p202407 VALUES LESS THAN ('2024-07-01 00:00:00'),
                PARTITION p202408 VALUES LESS THAN ('2024-08-01 00:00:00'),
                PARTITION p202409 VALUES LESS THAN ('2024-09-01 00:00:00'),
                PARTITION p202410 VALUES LESS THAN ('2024-10-01 00:00:00'),
                PARTITION p202411 VALUES LESS THAN ('2024-11-01 00:00:00'),
                PARTITION p202412 VALUES LESS THAN ('2024-12-01 00:00:00'),
                PARTITION p202501 VALUES LESS THAN ('2025-01-01 00:00:00'),
                PARTITION p202502 VALUES LESS THAN ('2025-02-01 00:00:00'),
                PARTITION p202503 VALUES LESS THAN ('2025-03-01 00:00:00'),
                PARTITION p202504 VALUES LESS THAN ('2025-04-01 00:00:00'),
                PARTITION p202505 VALUES LESS THAN ('2025-05-01 00:00:00'),
                PARTITION p202506 VALUES LESS THAN ('2025-06-01 00:00:00'),
                PARTITION p202507 VALUES LESS THAN ('2025-07-01 00:00:00'),
                PARTITION p202508 VALUES LESS THAN ('2025-08-01 00:00:00'),
                PARTITION p202509 VALUES LESS THAN ('2025-09-01 00:00:00'),
                PARTITION p202510 VALUES LESS THAN ('2025-10-01 00:00:00'),
                PARTITION p202511 VALUES LESS THAN ('2025-11-01 00:00:00'),
                PARTITION p202512 VALUES LESS THAN ('2025-12-01 00:00:00'),
                PARTITION p202601 VALUES LESS THAN ('2026-01-01 00:00:00'),
                PARTITION p202602 VALUES LESS THAN ('2026-02-01 00:00:00'),
                PARTITION p202603 VALUES LESS THAN ('2026-03-01 00:00:00'),
                PARTITION p202604 VALUES LESS THAN ('2026-04-01 00:00:00'),
                PARTITION p202605 VALUES LESS THAN ('2026-05-01 00:00:00'),
                PARTITION p202606 VALUES LESS THAN ('2026-06-01 00:00:00'),
                PARTITION p202607 VALUES LESS THAN ('2026-07-01 00:00:00'),
                PARTITION p202608 VALUES LESS THAN ('2026-08-01 00:00:00'),
                PARTITION p202609 VALUES LESS THAN ('2026-09-01 00:00:00'),
                PARTITION p202610 VALUES LESS THAN ('2026-10-01 00:00:00'),
                PARTITION p202611 VALUES LESS THAN ('2026-11-01 00:00:00'),
                PARTITION p202612 VALUES LESS THAN ('2026-12-01 00:00:00'),
                PARTITION p999999 VALUES LESS THAN (MAXVALUE)
                )
                DISTRIBUTED BY HASH(client_id) BUCKETS 30
                PROPERTIES (
                    "replication_num" = "1",
                    "enable_unique_key_merge_on_write" = "true"
                );
            """

        context.log.info(f"Dropping and re-initializing target ga_events_base table...")
        with conn.cursor() as cursor:
            cursor.execute(drop_table_sql)
            cursor.execute(init_table_sql)
        conn.commit()

        # get a list of ALL ga intraday events tables
        source_tables_sql = """
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'analytics'
              AND table_name REGEXP '^ga_events_[0-9]{8}$'
            order by table_name asc;
        """

    else:
        # clear out records newer than the backdate range, relative to the source tables we have
        clear_recent_records_sql = f"""
            with available_dates as (
                SELECT
                  table_name
                FROM information_schema.tables
                WHERE table_schema = 'analytics'
                  AND table_name REGEXP '^ga_events_[0-9]{{8}}$'
            ),
            backdated_sources as (
                select
                    table_name,
                    DATE_SUB(STR_TO_DATE(SUBSTRING_INDEX(table_name, '_', -1), '%Y%m%d'), INTERVAL 0 DAY) as intraday_date,
                    DATE_SUB(max(STR_TO_DATE(SUBSTRING_INDEX(table_name, '_', -1), '%Y%m%d')), INTERVAL {config.reload_days} DAY) as backdate_intraday_date
                from available_dates
                group by table_name
            ),
            max_backdate as (
                select max(backdate_intraday_date) as max_backdate_date
                from backdated_sources
            ),
            target_records as (
                select
                    ge.content_id
                from analytics.ga_events_base ge, max_backdate mb
                    where ge.created_date > mb.max_backdate_date
            )
            delete from analytics.ga_events_base
            where content_id in (select content_id from target_records);
        """

        context.log.info(f"Clearing target table records older than {config.reload_days} days...")
        with conn.cursor() as cursor:
            cursor.execute(clear_recent_records_sql)
        conn.commit()

        # get a list of MISSING and RECENT GA Intraday tables
        source_tables_sql = f"""
            with available_dates as (
                SELECT
                  table_name
                FROM information_schema.tables
                WHERE table_schema = 'analytics'
                  AND table_name REGEXP '^ga_events_[0-9]{{8}}$'
            ),
            backdated_sources as (
                select
                    table_name,
                    DATE_SUB(STR_TO_DATE(SUBSTRING_INDEX(table_name, '_', -1), '%Y%m%d'), INTERVAL 0 DAY) as intraday_date,
                    DATE_SUB(max(STR_TO_DATE(SUBSTRING_INDEX(table_name, '_', -1), '%Y%m%d')), INTERVAL {config.reload_days} DAY) as backdate_intraday_date
                from available_dates
                group by table_name
            ),
            max_backdate as (
                select max(backdate_intraday_date) as max_backdate_date
                from backdated_sources
            ),
            max_target_date as (
                select STR_TO_DATE(max(event_date), '%Y%m%d') as max_date
                from analytics.ga_events_base
            )
            select
                bs.table_name
            from backdated_sources bs, max_backdate mb, max_target_date mtd
            where bs.intraday_date > mb.max_backdate_date
            or bs.intraday_date > mtd.max_date
            order by intraday_date asc;
        """

    context.log.info(f"Pulling list of available intraday tables...")
    with conn.cursor() as cursor:
        cursor.execute(source_tables_sql)
        source_tables = [row[0] for row in cursor.fetchall()]
    conn.commit()
    source_batch_count = round(len(source_tables) / config.source_batch_size)

    for batch_idx in range(max(1, source_batch_count)):
        start_idx = batch_idx * config.source_batch_size
        end_idx = start_idx + config.source_batch_size
        current_batch = source_tables[start_idx:end_idx]


        context.log.info(f"Processing batch {batch_idx + 1} of {source_batch_count}")
        # Step 1: Create a temporary table with all source tables in the batch
        source_select_sql_prefix = """
            DROP TABLE IF EXISTS ga_events_base_assembly__tmp;
            CREATE TABLE analytics.ga_events_base_assembly__tmp AS
        """
        source_select_sql_body = ""
        for table in current_batch:
            source_select_sql_body += f"SELECT * FROM analytics.{table} UNION ALL "
        source_select_sql_body = source_select_sql_body.rstrip("UNION ALL ")
        source_select_sql = source_select_sql_prefix + source_select_sql_body + ";"

        context.log.info(f"Executing source_select SQL...")
        with conn.cursor() as cursor:
            cursor.execute(source_select_sql)
        conn.commit()

        # Step 2: Create a filtered temporary table with only the most recent version of each event record
        filter_temp_table_sql = """
            DROP TABLE IF EXISTS ga_events_base_assembly_filtered__tmp;
            CREATE TABLE analytics.ga_events_base_assembly_filtered__tmp AS
            WITH ranked_events AS (
                SELECT
                    *,
                    ROW_NUMBER() OVER (PARTITION BY hash_id ORDER BY ingest_timestamp DESC) as row_num
                FROM analytics.ga_events_base_assembly__tmp
            )
            SELECT
                content_id,
                created_date,
                client_id,
                hash_id,
                content_id_values,
                ingest_timestamp,
                id,
                org_id,
                ga4_id,
                export_date,
                bq_source_table,
                measurement_id,
                shop_domain_name,
                event_date,
                event_timestamp,
                event_name,
                page_location,
                ga_session_id,
                ga_session_number,
                page_referrer,
                -- user_params_source,
                -- user_params_campaign,
                -- user_params_medium,
                term,
                batch_page_id,
                page_title,
                session_engaged,
                batch_ordering_id,
                campaign as user_params_campaign,
                source as user_params_source,
                medium as user_params_medium,
                gclid,
                content,
                new_language,
                exp_variant_string,
                user_pseudo_id,
                stream_id,
                category,
                mobile_brand_name,
                mobile_model_name,
                mobile_marketing_name,
                mobile_os_hardware_model,
                operating_system,
                operating_system_version,
                browser_version,
                language,
                hostname,
                continent,
                country,
                region,
                city,
                sub_continent,
                metro,
                is_active_user,
                traffic_source_name,
                traffic_source_medium,
                traffic_source_source,
                traffic_source_manual_campaign_id,
                traffic_source_manual_campaign_name,
                traffic_source_manual_source,
                traffic_source_manual_medium,
                traffic_source_manual_term,
                traffic_source_manual_content,
                traffic_source_manual_source_platform,
                traffic_source_manual_creative_format,
                traffic_source_manual_marketing_tactic,
                traffic_source_gclid,
                traffic_source_dclid,
                traffic_source_srsltid,
                social_source_referral,
                ad_distribution_network,
                coreEventId,
                browser,
                app_load_id,
                buyflow_start_id
            FROM ranked_events
            WHERE row_num = 1;
        """

        context.log.info(f"Filtering for most recent event records by hash_id...")
        with conn.cursor() as cursor:
            cursor.execute(filter_temp_table_sql)
        conn.commit()

        # Insert the temp table content into the main body
        source_insert_sql = """
            set enable_insert_strict = false;
            set enable_unique_key_partial_update = false;

            insert into
                `analytics`.`ga_events_base` (
                    `content_id`,
                    `created_date`,
                    `client_id`,
                    `hash_id`,
                    `content_id_values`,
                    `ingest_timestamp`,
                    `id`,
                    `org_id`,
                    `ga4_id`,
                    `export_date`,
                    `bq_source_table`,
                    `measurement_id`,
                    `shop_domain_name`,
                    `event_date`,
                    `event_timestamp`,
                    `event_name`,
                    `page_location`,
                    `ga_session_id`,
                    `ga_session_number`,
                    `page_referrer`,
                    `user_params_source`,
                    `user_params_campaign`,
                    `user_params_medium`,
                    `term`,
                    `batch_page_id`,
                    `page_title`,
                    `session_engaged`,
                    `batch_ordering_id`,
                    -- `campaign`,
                    -- `source`,
                    -- `medium`,
                    `gclid`,
                    `content`,
                    `new_language`,
                    `exp_variant_string`,
                    `user_pseudo_id`,
                    `stream_id`,
                    `category`,
                    `mobile_brand_name`,
                    `mobile_model_name`,
                    `mobile_marketing_name`,
                    `mobile_os_hardware_model`,
                    `operating_system`,
                    `operating_system_version`,
                    `browser_version`,
                    `language`,
                    `hostname`,
                    `continent`,
                    `country`,
                    `region`,
                    `city`,
                    `sub_continent`,
                    `metro`,
                    `is_active_user`,
                    `traffic_source_name`,
                    `traffic_source_medium`,
                    `traffic_source_source`,
                    `traffic_source_manual_campaign_id`,
                    `traffic_source_manual_campaign_name`,
                    `traffic_source_manual_source`,
                    `traffic_source_manual_medium`,
                    `traffic_source_manual_term`,
                    `traffic_source_manual_content`,
                    `traffic_source_manual_source_platform`,
                    `traffic_source_manual_creative_format`,
                    `traffic_source_manual_marketing_tactic`,
                    `traffic_source_gclid`,
                    `traffic_source_dclid`,
                    `traffic_source_srsltid`,
                    `social_source_referral`,
                    `ad_distribution_network`,
                    `coreEventId`,
                    `browser`,
                    `app_load_id`,
                    `buyflow_start_id`
                )
                (
                select
                    `content_id`,
                    `created_date`,
                    `client_id`,
                    `hash_id`,
                    `content_id_values`,
                    `ingest_timestamp`,
                    `id`,
                    `org_id`,
                    `ga4_id`,
                    `export_date`,
                    `bq_source_table`,
                    `measurement_id`,
                    `shop_domain_name`,
                    `event_date`,
                    `event_timestamp`,
                    `event_name`,
                    `page_location`,
                    `ga_session_id`,
                    `ga_session_number`,
                    `page_referrer`,
                    `user_params_source`,
                    `user_params_campaign`,
                    `user_params_medium`,
                    `term`,
                    `batch_page_id`,
                    `page_title`,
                    `session_engaged`,
                    `batch_ordering_id`,
                    -- `campaign`,
                    -- `source`,
                    -- `medium`,
                    `gclid`,
                    `content`,
                    `new_language`,
                    `exp_variant_string`,
                    `user_pseudo_id`,
                    `stream_id`,
                    `category`,
                    `mobile_brand_name`,
                    `mobile_model_name`,
                    `mobile_marketing_name`,
                    `mobile_os_hardware_model`,
                    `operating_system`,
                    `operating_system_version`,
                    `browser_version`,
                    `language`,
                    `hostname`,
                    `continent`,
                    `country`,
                    `region`,
                    `city`,
                    `sub_continent`,
                    `metro`,
                    `is_active_user`,
                    `traffic_source_name`,
                    `traffic_source_medium`,
                    `traffic_source_source`,
                    `traffic_source_manual_campaign_id`,
                    `traffic_source_manual_campaign_name`,
                    `traffic_source_manual_source`,
                    `traffic_source_manual_medium`,
                    `traffic_source_manual_term`,
                    `traffic_source_manual_content`,
                    `traffic_source_manual_source_platform`,
                    `traffic_source_manual_creative_format`,
                    `traffic_source_manual_marketing_tactic`,
                    `traffic_source_gclid`,
                    `traffic_source_dclid`,
                    `traffic_source_srsltid`,
                    `social_source_referral`,
                    `ad_distribution_network`,
                    `coreEventId`,
                    `browser`,
                    `app_load_id`,
                    `buyflow_start_id`
                from
                  analytics.ga_events_base_assembly_filtered__tmp
              )
            """
        source_insert_sql = source_insert_sql + ';'

        context.log.info(f"Executing insertion SQL...")
        with conn.cursor() as cursor:
            cursor.execute(source_insert_sql)
        conn.commit()

        # Clean up temporary tables
        cleanup_sql = """
            DROP TABLE IF EXISTS analytics.ga_events_base_assembly__tmp;
            DROP TABLE IF EXISTS analytics.ga_events_base_assembly_filtered__tmp;
        """

        context.log.info(f"Cleaning up temporary tables...")
        with conn.cursor() as cursor:
            cursor.execute(cleanup_sql)
        conn.commit()

    return "ga_events_base"
