{{ config(
    materialized='incremental',
    unique_key=['id','createDate'],
    distributed_by=['id','createDate'],
    buckets=20,
    incremental_strategy = "append",
    partition_by='createDate',
    partition_type='range',
    partition_by_init=[
        "PARTITION p202306 VALUES LESS THAN ('2023-06-01')",
        "PARTITION p202307 VALUES LESS THAN ('2023-07-01')",
        "PARTITION p202308 VALUES LESS THAN ('2023-08-01')",
        "PARTITION p202309 VALUES LESS THAN ('2023-09-01')",
        "PARTITION p202310 VALUES LESS THAN ('2023-10-01')",
        "PARTITION p202311 VALUES LESS THAN ('2023-11-01')",
        "PARTITION p202312 VALUES LESS THAN ('2023-12-01')",
        "PARTITION p202401 VALUES LESS THAN ('2024-01-01')",
        "PARTITION p202402 VALUES LESS THAN ('2024-02-01')",
        "PARTITION p202403 VALUES LESS THAN ('2024-03-01')",
        "PARTITION p202404 VALUES LESS THAN ('2024-04-01')",
        "PARTITION p202405 VALUES LESS THAN ('2024-05-01')",
        "PARTITION p202406 VALUES LESS THAN ('2024-06-01')",
        "PARTITION p202407 VALUES LESS THAN ('2024-07-01')",
        "PARTITION p202408 VALUES LESS THAN ('2024-08-01')",
        "PARTITION p202409 VALUES LESS THAN ('2024-09-01')",
        "PARTITION p202410 VALUES LESS THAN ('2024-10-01')",
        "PARTITION p202411 VALUES LESS THAN ('2024-11-01')",
        "PARTITION p202412 VALUES LESS THAN ('2024-12-01')",
        "PARTITION p202501 VALUES LESS THAN ('2025-01-01')",
        "PARTITION p202502 VALUES LESS THAN ('2025-02-01')",
        "PARTITION p202503 VALUES LESS THAN ('2025-03-01')",
        "PARTITION p202504 VALUES LESS THAN ('2025-04-01')",
        "PARTITION p202505 VALUES LESS THAN ('2025-05-01')",
        "PARTITION p202506 VALUES LESS THAN ('2025-06-01')",
        "PARTITION p202507 VALUES LESS THAN ('2025-07-01')",
        "PARTITION p202508 VALUES LESS THAN ('2025-08-01')",
        "PARTITION p202509 VALUES LESS THAN ('2025-09-01')",
        "PARTITION p202510 VALUES LESS THAN ('2025-10-01')",
        "PARTITION p202511 VALUES LESS THAN ('2025-11-01')",
        "PARTITION p202512 VALUES LESS THAN ('2025-12-01')"
      ]
) }}

{# Get the max ID value date for this model #}
{% if is_incremental() %}
    {% set q_max_id %}
        select max(id) as latest from {{ this }}
    {% endset %}
    {% set max_id = run_query(q_max_id) %}
    {% if execute %}
        {% set max_id_value = max_id.columns[0].values()[0] %}
    {% else %}
        {% set max_id_value = '0' %}
    {% endif %}
{% else %}
    {% set max_id_value = '0' %}
{% endif %}

select
    id,
    createDate,
    orgId,
    clientId,
    event_name,
    host,
    page_url,
    page_location,
    page_title,
    gcid,
    session_id,
    session_number,
    shopper_type,
    market,
    offer_set,
    package_change_type,
    promotion_id,
    promotion_name,
    creative_name,
    creative_slot,
    credit_bureau,
    credit_check_type,
    credit_check_result,
    input_address,
    order_total,
    order_monthly_total,
    shopperId,
    orderId,
    app_load_id,
    buyflow_start_id,
    lead_capture_form_name,
    app_name,
    agent,
    edited_agent,
    field_changed,
    new_val,
    old_val,
    ticketId,
    qualityScore,
    event_params,
    unique_plan_id,
    event_timestamp

from {{ remote_source('psql_prod_public','doris_core_event','atlas_psql_prod') }}
{% if is_incremental() %}
    where id > ({{ max_id_value }})
{% endif %}
limit 1000000
