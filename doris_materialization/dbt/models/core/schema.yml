
version: 2

models:
  - name: core_events_raw
    description: "Incrementally updated events from the Atlas Core app"
    meta:
      dagster:
        group: "core"
  - name: core_events_base
    description: "Cleaned, filtered, validated atlas core events"
    meta:
      dagster:
        group: "core"

sources:
  - name: psql_prod_public
    schema: public
    description: Remote Catalog to prod postgres
    tables:
      - name: doris_core_event
        description: "Internal events record from all buyflows, presorted by ID ascending"
