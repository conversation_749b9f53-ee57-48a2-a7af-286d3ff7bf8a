SELECT
    cast(id as bigint) AS coreEventId,
    cast(id as varchar(200)) AS id,
    createDate AS updateDate,
    cast(orderId as bigint) AS orderId,

    CASE WHEN session_number regexp '^\\d+$'
        THEN session_number
        ELSE NULL
    END AS session_number,

    CASE
        WHEN session_id regexp '^(|\\d+)$' THEN session_id
        ELSE NULL
    END AS session_id,

    CASE
        WHEN event_name regexp '^[a-zA-Z_-]+$' THEN event_name
        ELSE NULL
    END AS event_name,

    CASE
        WHEN gcid regexp '^\\d+\\.\\d+$' THEN gcid
        ELSE NULL
    END AS gcid,

    CASE
        WHEN host regexp '^[a-zA-Z0-9][-a-zA-Z0-9.]*[a-zA-Z0-9](\\.[a-zA-Z0-9][-a-zA-Z0-9.]*[a-zA-Z0-9])*$' THEN host
        ELSE NULL
    END AS host,

    orgId,
    clientId,
    page_url,
    page_location,
    page_title,
    shopper_type,
    market,
    offer_set,
    package_change_type,
    promotion_id,
    promotion_name,
    creative_name,
    creative_slot,
    cast(order_total as decimal(10, 2)) AS order_total,
    cast(order_monthly_total as decimal(10, 2)) AS order_monthly_total,
    shopperId,
    createDate,
    credit_bureau,
    credit_check_type,
    credit_check_result,
    input_address,
    app_load_id,
    buyflow_start_id,
    cast((UNIX_TIMESTAMP(createDate) * 1000000) as bigint) AS event_timestamp,
    event_params,
    unique_plan_id
FROM {{ ref('core_events_raw') }}
WHERE app_load_id IS NOT NULL
AND buyflow_start_id IS NOT NULL
