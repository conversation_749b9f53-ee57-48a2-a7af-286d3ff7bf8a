{% set ga_event_date = var("ga_event_date", "") %}
{% if ga_event_date == "" %}

    {# Get the most recent date for intraday exports in the remote catalog #}
    {% set q_recent_analytics_date %}
        with analytics_dates as
         (
            select split_part(table_name, '_', 3) as event_date
            from {{ source('analytics','bq_analytics_info_schema') }}
            where catalog_name = 'atlas_bigquery_prd'
        )
        select max(event_date) as recent_date from analytics_dates
        where event_date REGEXP '^20[0-9]+$'
        and length(event_date) < 9;
    {% endset %}

    {% set recent_analytics_dates = run_query(q_recent_analytics_date) %}
    {% if execute %}
        {% set recent_analytics_date = recent_analytics_dates.columns[0].values()[0] %}
    {% else %}
        {% set recent_analytics_date = '' %}
    {% endif %}
{% else %}
    {% set recent_analytics_date = ga_event_date %}
{% endif %}

{# Get all client IDs that have intraday export tables in the remote catalog for the selected date #}
{% set q_analytics_clients %}
with client_ids as (
    select split_part(database_name, '_', 2) as client_id
    from {{ source('analytics','bq_analytics_info_schema') }}
    where table_name = 'events_intraday_{{ recent_analytics_date }}'
    and catalog_name = 'atlas_bigquery_prd'
)
select distinct client_id from client_ids
{% endset %}

{% set analytics_clients = run_query(q_analytics_clients) %}
{% if execute %}
    {% set list_client_ids = analytics_clients.columns[0].values() %}
{% else %}
    {% set list_client_ids = [] %}
{% endif %}


{% set column_to_check = "is_active_user" %}
{% set sample_client_id = list_client_ids[0] %}

{% set table_check_query %}
    describe atlas_bigquery_prd.analytics_{{ sample_client_id }}.events_intraday_{{ recent_analytics_date }}
{% endset %}

{% set table_check_results = run_query(table_check_query) %}
{% set found_value = false %}

{% if execute %}
  {% for row in table_check_results.rows %}
    {% if row[0] == column_to_check %}
      {% set found_value = true %}
        {% break %}
    {% endif %}
  {% endfor %}
{% endif %}


{% for client_id in list_client_ids %}
select
    '{{ client_id }}' as ga4_id,
    '{{ recent_analytics_date }}' as export_date,
    'analytics_{{ client_id }}.events_intraday_{{ recent_analytics_date }}' as bq_source_table,
    event_date,
    event_timestamp,
    event_name,
    event_params,
    event_previous_timestamp,
    event_value_in_usd,
    event_bundle_sequence_id,
    event_server_timestamp_offset,
    user_id,
    user_pseudo_id,
    privacy_info,
    user_properties,
    user_first_touch_timestamp,
    user_ltv,
    device,
    geo,
    app_info,
    traffic_source,
    stream_id,
    platform,
    event_dimensions,
    ecommerce,
    items,
    NAMED_STRUCT(
        'manual_campaign_id', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_campaign_id'),
        'manual_campaign_name', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_campaign_name'),
        'manual_source', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_source'),
        'manual_medium', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_medium'),
        'manual_term', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_term'),
        'manual_content', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_content'),
        'manual_source_platform', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_source_platform'),
        'manual_creative_format', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_creative_format'),
        'manual_marketing_tactic', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.manual_marketing_tactic'),
        'gclid', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.gclid'),
        'dclid', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.dclid'),
        'srsltid', JSON_EXTRACT_STRING(CAST(collected_traffic_source AS JSON), '$.srsltid')
    ) as collected_traffic_source,
    {% if found_value %}
    is_active_user
    {% else %}
    null as is_active_user
    {% endif %}

from atlas_bigquery_prd.analytics_{{ client_id }}.events_intraday_{{ recent_analytics_date }}
{% if not loop.last %}
union all
{% endif %}
{% endfor %}
