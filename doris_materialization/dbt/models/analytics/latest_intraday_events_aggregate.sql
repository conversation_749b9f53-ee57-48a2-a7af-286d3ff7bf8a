-- depends on {{ source('analytics','ga_events_dated_snapshot') }}
{{ config(
    materialized='table',
    unique_key=['content_id'],
    distributed_by=['content_id', 'hash_id'],
    buckets=20
) }}

{% set ga_event_date = var("ga_event_date", "") %}
{% if ga_event_date == "" %}

    {# Get the most recent date for intraday exports in the remote catalog #}
    {% set q_recent_analytics_date %}
        with analytics_dates as
         (
            select split_part(table_name, '_', 3) as event_date
            from {{ source('analytics','bq_analytics_info_schema') }}
            where catalog_name = 'atlas_bigquery_prd'
        )
        select max(event_date) as recent_date from analytics_dates
        where event_date REGEXP '^20[0-9]+$'
        and length(event_date) < 9;
    {% endset %}

    {% set recent_analytics_dates = run_query(q_recent_analytics_date) %}
    {% if execute %}
        {% set recent_analytics_date = recent_analytics_dates.columns[0].values()[0] %}
    {% else %}
        {% set recent_analytics_date = '' %}
    {% endif %}
{% else %}
    {% set recent_analytics_date = ga_event_date %}
{% endif %}

{% set q_snapshot_tables %}
SHOW TABLES from analytics LIKE 'ga_events_snapshot_{{ recent_analytics_date }}_%';
{% endset %}

-- executing snapshot_tables query {{ q_snapshot_tables | replace('\n', '') }}
{% set snapshot_tables = run_query(q_snapshot_tables) %}
{% if execute %}
    {% set list_snapshot_tables = snapshot_tables.columns[0].values() %}
    -- results:
    {% for snapshot_table in list_snapshot_tables %}
        -- {{ snapshot_table }}
    {% endfor %}
{% endif %}

{% set q_final_tables %}
SHOW TABLES from analytics LIKE 'ga_events_{{ recent_analytics_date }}';
{% endset %}

-- executing final_table query {{ final_table | replace('\n', '') }}
{% set final_tables = run_query(q_final_tables) %}
{% if execute %}
    -- results:
    {% set list_final_tables = final_tables.columns[0].values() %}
    {% for final_table in list_final_tables %}
        -- {{ final_table }}
    {% endfor %}
    {% set list_all_tables = list_snapshot_tables + list_final_tables %}
{% endif %}

with
union_timestamps as (
    {% for source_table in list_all_tables %}
    select distinct
        content_id,
        ingest_timestamp
    from analytics.{{ source_table }}
    {% if not loop.last %}
    union
    {% endif %}
    {% endfor %}
),
content_earliest_seen as (
    select
        content_id,
        min(ingest_timestamp) as ingest_timestamp
    from union_timestamps
    group by content_id
),
union_events as (
    {% for source_table in list_all_tables %}
    select
        content_id,
        hash_id,
        content_id_values,
        id,
        client_id,
        org_id,
        ga4_id,
        export_date,
        bq_source_table,
        measurement_id,
        shop_domain_name,
        event_date,
        event_timestamp,
        created_date,
        event_name,
        page_location,
        ga_session_id,
        ga_session_number,
        page_referrer,
        user_params_source,
        user_params_campaign,
        user_params_medium,
        term,
        batch_page_id,
        page_title,
        session_engaged,
        batch_ordering_id,
        campaign,
        `source`,
        medium,
        gclid,
        content,
        new_language,
        exp_variant_string,
        user_pseudo_id,
        stream_id,
        category,
        mobile_brand_name,
        mobile_model_name,
        mobile_marketing_name,
        mobile_os_hardware_model,
        operating_system,
        operating_system_version,
        browser_version,
        `language`,
        hostname,
        continent,
        country,
        region,
        city,
        sub_continent,
        metro,
        is_active_user,
        traffic_source_name,
        traffic_source_medium,
        traffic_source_source,
        traffic_source_manual_campaign_id,
        traffic_source_manual_campaign_name,
        traffic_source_manual_source,
        traffic_source_manual_medium,
        traffic_source_manual_term,
        traffic_source_manual_content,
        traffic_source_manual_source_platform,
        traffic_source_manual_creative_format,
        traffic_source_manual_marketing_tactic,
        traffic_source_gclid,
        traffic_source_dclid,
        traffic_source_srsltid,
        social_source_referral,
        ad_distribution_network,
        coreEventId,
        browser,
        app_load_id,
        buyflow_start_id
        from analytics.{{ source_table }}

    {% if not loop.last %}
    union
    {% endif %}
    {% endfor %}
),
dedupe_union_events as (
    select
        content_id,
        hash_id,
        content_id_values,
        id,
        client_id,
        org_id,
        ga4_id,
        export_date,
        bq_source_table,
        measurement_id,
        shop_domain_name,
        event_date,
        event_timestamp,
        created_date,
        event_name,
        page_location,
        ga_session_id,
        ga_session_number,
        page_referrer,
        user_params_source,
        user_params_campaign,
        user_params_medium,
        term,
        batch_page_id,
        page_title,
        session_engaged,
        batch_ordering_id,
        campaign,
        `source`,
        medium,
        gclid,
        content,
        new_language,
        exp_variant_string,
        user_pseudo_id,
        stream_id,
        category,
        mobile_brand_name,
        mobile_model_name,
        mobile_marketing_name,
        mobile_os_hardware_model,
        operating_system,
        operating_system_version,
        browser_version,
        `language`,
        hostname,
        continent,
        country,
        region,
        city,
        sub_continent,
        metro,
        is_active_user,
        traffic_source_name,
        traffic_source_medium,
        traffic_source_source,
        traffic_source_manual_campaign_id,
        traffic_source_manual_campaign_name,
        traffic_source_manual_source,
        traffic_source_manual_medium,
        traffic_source_manual_term,
        traffic_source_manual_content,
        traffic_source_manual_source_platform,
        traffic_source_manual_creative_format,
        traffic_source_manual_marketing_tactic,
        traffic_source_gclid,
        traffic_source_dclid,
        traffic_source_srsltid,
        social_source_referral,
        ad_distribution_network,
        coreEventId,
        browser,
        app_load_id,
        buyflow_start_id

        from (
        select
            ROW_NUMBER() OVER (PARTITION BY content_id) AS event_number,
            *
            from union_events
    ) as dedupe
    where event_number = 1
)

select
    ue.*,
    ces.ingest_timestamp
from dedupe_union_events ue
left join content_earliest_seen ces on ces.content_id = ue.content_id
