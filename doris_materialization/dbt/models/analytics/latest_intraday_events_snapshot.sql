with base_events as (
    select
        XXHASH_64(CONCAT(ga4_id, ifnull(user_pseudo_id,''), ifnull(user_first_touch_timestamp,''), ifnull(event_timestamp,''), ifnull(event_name,''), ifnull(event_bundle_sequence_id,''))) AS hash_id,
        ga4_id,
        export_date,
        bq_source_table,
        event_date,
        event_timestamp,
        event_name,
        event_params,
        event_previous_timestamp,
        event_value_in_usd,
        event_bundle_sequence_id,
        event_server_timestamp_offset,
        user_id,
        user_pseudo_id,
        privacy_info,
        user_properties,
        user_first_touch_timestamp,
        user_ltv,
        device,
        geo,
        app_info,
        traffic_source,
        stream_id,
        platform,
        event_dimensions,
        ecommerce,
        items,
        collected_traffic_source,
        is_active_user

    FROM {{ ref('latest_intraday_events_raw') }}
),
exploded_event_params as (
    select * from (
        SELECT
            hash_id,
            struct_element(event_param, 'key') as param_key,
            struct_element(struct_element(event_param, 'value'),'string_value') as string_param,
            struct_element(struct_element(event_param, 'value'),'int_value') as int_param,
            struct_element(struct_element(event_param, 'value'),'float_value') as float_param,
            struct_element(struct_element(event_param, 'value'),'double_value') as double_param
        FROM base_events
        LATERAL VIEW explode(event_params) tmp1 AS event_param
    ) ep
),
hashed_content as (
    select
        hash_id,
        concat('{', exploded_param_values, ',', extra_param_values, '}') as content_id_values,
        XXHASH_64(concat('{', exploded_param_values, ',', extra_param_values, '}')) as content_id

    from (
        select
            epv.hash_id,
            group_concat(concat('"', epv.param_key, '":', ifnull(epv.param_values,'')),',' order by epv.param_key, epv.param_values) as exploded_param_values,
            max(spv.extra_params) as extra_param_values
        from (
            SELECT
                hash_id,
                struct_element(event_param, 'key') as param_key,
                concat(
                    '["',
                    ifnull(struct_element(struct_element(event_param, 'value'),'string_value'),''), '",',
                    '"', ifnull(cast(struct_element(struct_element(event_param, 'value'),'int_value') as TEXT),''), '",',
                    '"', ifnull(cast(struct_element(struct_element(event_param, 'value'),'float_value') as TEXT),''), '",',
                    '"', ifnull(cast(struct_element(struct_element(event_param, 'value'),'double_value') as TEXT),''), '"]'
                ) as param_values
            FROM base_events
            LATERAL VIEW explode(event_params) tmp1 AS event_param
            where hash_id is not null
        ) epv
        join (
            SELECT
                hash_id,
                concat(
                    '"hash_id":"', hash_id, '",',
                    '"device/category":"',replace(ifnull(struct_element(device, 'category'), ''), '"', ''), '",',
                    '"device/mobile_brand_name":"',replace(ifnull(struct_element(device, 'mobile_brand_name'), ''), '"', ''), '",',
                    '"device/mobile_model_name":"',replace(ifnull(struct_element(device, 'mobile_model_name'), ''), '"', ''), '",',
                    '"device/mobile_marketing_name":"',replace(ifnull(struct_element(device, 'mobile_marketing_name'), ''), '"', ''), '",',
                    '"device/mobile_os_hardware_model":"',replace(ifnull(struct_element(device, 'mobile_os_hardware_model'), ''), '"', ''), '",',
                    '"device/operating_system":"',replace(ifnull(struct_element(device, 'operating_system'), ''), '"', ''), '",',
                    '"device/operating_system_version":"',replace(ifnull(struct_element(device, 'operating_system_version'), ''), '"', ''), '",',
                    '"device/web_info/browser_version":"',replace(ifnull(struct_element(struct_element(device, 'web_info'),'browser_version'), ''), '"', ''), '",',
                    '"device/language":"',replace(ifnull(struct_element(device, 'language'), ''), '"', ''), '",',
                    '"device/web_info/hostname":"',replace(ifnull(struct_element(struct_element(device, 'web_info'),'hostname'), ''), '"', ''), '",',
                    '"continent":"',replace(ifnull(struct_element(geo, 'continent'), ''), '"', ''), '",',
                    '"country":"',replace(ifnull(struct_element(geo, 'country'), ''), '"', ''), '",',
                    '"region":"',replace(ifnull(struct_element(geo, 'region'), ''), '"', ''), '",',
                    '"city":"',replace(ifnull(struct_element(geo, 'city'), ''), '"', ''), '",',
                    '"sub_continent":"',replace(ifnull(struct_element(geo, 'sub_continent'), ''), '"', ''), '",',
                    '"metro":"',replace(ifnull(struct_element(geo, 'metro'), ''), '"', ''), '",',
                    '"name":"',replace(ifnull(struct_element(traffic_source, 'name'), ''), '"', ''), '",',
                    '"medium":"',replace(ifnull(struct_element(traffic_source, 'medium'), ''), '"', ''), '",',
                    '"source":"',replace(ifnull(struct_element(traffic_source, 'source'), ''), '"', ''), '",',
                    '"manual_campaign_id":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_campaign_id'), ''), '"', ''), '",',
                    '"manual_campaign_name":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_campaign_name'), ''), '"', ''), '",',
                    '"manual_source":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_source'), ''), '"', ''), '",',
                    '"manual_medium":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_medium'), ''), '"', ''), '",',
                    '"manual_term":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_term'), ''), '"', ''), '",',
                    '"manual_content":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_content'), ''), '"', ''), '",',
                    '"manual_source_platform":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_source_platform'), ''), '"', ''), '",',
                    '"manual_creative_format":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_creative_format'), ''), '"', ''), '",',
                    '"manual_marketing_tactic":"',replace(ifnull(struct_element(collected_traffic_source, 'manual_marketing_tactic'), ''), '"', ''), '",',
                    '"gclid":"',replace(ifnull(struct_element(collected_traffic_source, 'gclid'), ''), '"', ''), '",',
                    '"dclid":"',replace(ifnull(struct_element(collected_traffic_source, 'dclid'), ''), '"', ''), '",',
                    '"srsltid":"',replace(ifnull(struct_element(collected_traffic_source, 'srsltid'), ''), '"', ''), '",',
                    '"device/web_info/browser":"',replace(ifnull(struct_element(struct_element(device, 'web_info'),'browser'), ''), '"', ''), '"'
                ) as extra_params
            from base_events
        ) spv
        on spv.hash_id = epv.hash_id
        group by hash_id
    ) agg_content_hash
),
selected_event_params as (
    SELECT
        hash_id,
        MAX(CASE WHEN param_key = 'page_location' THEN string_param END) AS page_location,
        MAX(CASE WHEN param_key = 'ga_session_id' THEN int_param END) AS ga_session_id,
        MAX(CASE WHEN param_key = 'ga_session_number' THEN int_param END) AS ga_session_number,
        MAX(CASE WHEN param_key = 'page_referrer' THEN string_param END) AS page_referrer,
        MAX(CASE WHEN param_key = 'user_params_source' THEN string_param END) AS user_params_source,
        MAX(CASE WHEN param_key = 'user_params_campaign' THEN string_param END) AS user_params_campaign,
        MAX(CASE WHEN param_key = 'user_params_medium' THEN string_param END) AS user_params_medium,
        MAX(CASE WHEN param_key = 'term' THEN string_param END) AS term,
        MAX(CASE WHEN param_key = 'batch_page_id' THEN int_param END) AS batch_page_id,
        MAX(CASE WHEN param_key = 'page_title' THEN string_param END) AS page_title,
        MAX(CASE WHEN param_key = 'session_engaged' THEN string_param END) AS session_engaged,
        MAX(CASE WHEN param_key = 'batch_ordering_id' THEN int_param END) AS batch_ordering_id,
        MAX(CASE WHEN param_key = 'campaign' THEN string_param END) AS campaign,
        MAX(CASE WHEN param_key = 'source' THEN string_param END) AS source,
        MAX(CASE WHEN param_key = 'medium' THEN string_param END) AS medium,
        MAX(CASE WHEN param_key = 'gclid' THEN string_param END) AS gclid,
        MAX(CASE WHEN param_key = 'content' THEN string_param END) AS content,
        MAX(CASE WHEN param_key = 'new_language' THEN string_param END) AS new_language,
        MAX(CASE WHEN param_key = 'exp_variant_string' THEN string_param END) AS exp_variant_string,
        MAX(CASE WHEN param_key = 'app_load_id' THEN string_param END) AS app_load_id,
        MAX(CASE WHEN param_key = 'buyflow_start_id' THEN string_param END) AS buyflow_start_id
    FROM
        exploded_event_params
    GROUP BY hash_id
),
joined_events as (
    select
        be.hash_id,
        hc.content_id,
        hc.content_id_values,
        c.client_id as client_id,
        c.org_id as org_id,
        be.ga4_id,
        be.export_date,
        be.bq_source_table,
        c.measurement_id,
        c.dns as shop_domain_name,
        be.event_date,
        be.event_timestamp,
        FROM_UNIXTIME(be.event_timestamp / 1000000) as created_date,
        event_name,
        sep.page_location,
        sep.ga_session_id,
        sep.ga_session_number,
        sep.page_referrer,
        sep.user_params_source,
        sep.user_params_campaign,
        sep.user_params_medium,
        sep.term,
        sep.batch_page_id,
        sep.page_title,
        sep.session_engaged,
        sep.batch_ordering_id,
        sep.campaign,
        sep.source,
        sep.medium,
        sep.gclid,
        sep.content,
        sep.new_language,
        sep.exp_variant_string,
        be.user_pseudo_id,
        be.stream_id,
        struct_element(device, 'category') as category,
        struct_element(device, 'mobile_brand_name') as mobile_brand_name,
        struct_element(device, 'mobile_model_name') as mobile_model_name,
        struct_element(device, 'mobile_marketing_name') as mobile_marketing_name,
        struct_element(device, 'mobile_os_hardware_model') as mobile_os_hardware_model,
        struct_element(device, 'operating_system') as operating_system,
        struct_element(device, 'operating_system_version') as operating_system_version,
        struct_element(struct_element(device, 'web_info'),'browser_version') as browser_version,
        struct_element(device, 'language') as `language`,
        struct_element(struct_element(device, 'web_info'),'hostname') as hostname,
        struct_element(geo, 'continent') as continent,
        struct_element(geo, 'country') as country,
        struct_element(geo, 'region') as region,
        struct_element(geo, 'city') as city,
        struct_element(geo, 'sub_continent') as sub_continent,
        struct_element(geo, 'metro') as metro,
        be.is_active_user,
        struct_element(traffic_source, 'name') as traffic_source_name,
        struct_element(traffic_source, 'medium') as traffic_source_medium,
        struct_element(traffic_source, 'source') as traffic_source_source,
        struct_element(collected_traffic_source, 'manual_campaign_id') as traffic_source_manual_campaign_id,
        struct_element(collected_traffic_source, 'manual_campaign_name') as traffic_source_manual_campaign_name,
        struct_element(collected_traffic_source, 'manual_source') as traffic_source_manual_source,
        struct_element(collected_traffic_source, 'manual_medium') as traffic_source_manual_medium,
        struct_element(collected_traffic_source, 'manual_term') as traffic_source_manual_term,
        struct_element(collected_traffic_source, 'manual_content') as traffic_source_manual_content,
        struct_element(collected_traffic_source, 'manual_source_platform') as traffic_source_manual_source_platform,
        struct_element(collected_traffic_source, 'manual_creative_format') as traffic_source_manual_creative_format,
        struct_element(collected_traffic_source, 'manual_marketing_tactic') as traffic_source_manual_marketing_tactic,
        struct_element(collected_traffic_source, 'gclid') as traffic_source_gclid,
        struct_element(collected_traffic_source, 'dclid') as traffic_source_dclid,
        struct_element(collected_traffic_source, 'srsltid') as traffic_source_srsltid,
        null as social_Source_referral,
        null as ad_distribution_network,
        0 as coreEventId,
        struct_element(struct_element(device, 'web_info'),'browser') as browser,
        sep.app_load_id,
        sep.buyflow_start_id

    from base_events be
    left join {{ source('warehouse','clients') }} c on c.ga4_id = be.ga4_id
    left join selected_event_params sep on sep.hash_id = be.hash_id
    left join hashed_content hc on hc.hash_id = be.hash_id
),
numbered_events as (
    SELECT
        *,
        event_date as _suffix,
        ROW_NUMBER() OVER (
            partition by event_date
            order by event_timestamp, ga_session_id, event_name
        ) as _row
    FROM
        joined_events
)

select
    content_id,
    hash_id,
    CAST(content_id_values AS TEXT) as content_id_values,
    CURRENT_TIMESTAMP() as ingest_timestamp,
    concat(client_id, '-', _suffix, '-', _row) as id,
    client_id,
    org_id,
    cast(ga4_id as BIGINT) as ga4_id,
    cast(export_date as BIGINT) as export_date,
    bq_source_table,
    measurement_id,
    shop_domain_name,
    cast(event_date as BIGINT) as event_date,
    event_timestamp,
    STR_TO_DATE(created_date, '%Y-%m-%d %H:%i:%s') AS created_date,
    event_name,
    page_location,
    cast(ga_session_id as BIGINT) as ga_session_id,
    ga_session_number,
    page_referrer,
    user_params_source,
    user_params_campaign,
    user_params_medium,
    term,
    cast(batch_page_id as BIGINT) as batch_page_id,
    page_title,
    session_engaged,
    batch_ordering_id,
    campaign,
    `source`,
    medium,
    gclid,
    content,
    new_language,
    exp_variant_string,
    user_pseudo_id,
    cast(stream_id as BIGINT) as stream_id,
    category,
    mobile_brand_name,
    mobile_model_name,
    mobile_marketing_name,
    mobile_os_hardware_model,
    operating_system,
    operating_system_version,
    browser_version,
    `language`,
    hostname,
    continent,
    country,
    region,
    city,
    sub_continent,
    metro,
    is_active_user,
    traffic_source_name,
    traffic_source_medium,
    traffic_source_source,
    traffic_source_manual_campaign_id,
    traffic_source_manual_campaign_name,
    traffic_source_manual_source,
    traffic_source_manual_medium,
    traffic_source_manual_term,
    traffic_source_manual_content,
    traffic_source_manual_source_platform,
    traffic_source_manual_creative_format,
    traffic_source_manual_marketing_tactic,
    traffic_source_gclid,
    traffic_source_dclid,
    traffic_source_srsltid,
    social_source_referral,
    ad_distribution_network,
    coreEventId,
    browser,
    app_load_id,
    buyflow_start_id

from numbered_events
where
    event_name in (
        'session_start',
        'first_visit',
        'user_engagement',
        'buyflow_start',
        'experience_impression',
        'spanish_translation'
    )
    or (
        event_name = 'page_view'
        and hostname != shop_domain_name
    )
