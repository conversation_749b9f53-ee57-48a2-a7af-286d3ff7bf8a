
version: 2

models:
  - name: latest_intraday_events_raw
    description: "Takes GA intraday events for all clients on the most recent date, consolidating all events to a single local table"
    meta:
      dagster:
        group: "analytics"
  - name: latest_intraday_events_snapshot
    description: "Refines intraday events, expanding JSON fields and integrating client specific details, saving a snapshot of the events state for later integration"
    meta:
      dagster:
        group: "analytics"
  - name: latest_intraday_events_aggregate
    description: "Joins all snapshots for the given event date into a single table of unique values"
    meta:
      dagster:
        group: "analytics"
        deps: "ga_events_dated_snapshot"

sources:
  - name: analytics
    schema: analytics
    description: Apache Doris lakehouse (analytics schema)
    tables:
      - name: bq_analytics_info_schema
        freshness: null
        meta:
          dagster:
            asset_key: ["bq_analytics_info_schema"]
      - name: ga_events_dated_snapshot
        meta:
          dagster:
            asset_key: [ "ga_events_dated_snapshot" ]
      - name: ga_events_dated_final
        meta:
          dagster:
            asset_key: [ "ga_events_dated_final" ]
      - name: ga_init_events
        meta:
          dagster:
            asset_key: [ "ga_init_events" ]

  - name: warehouse
    schema: warehouse
    description: Apache Doris lakehouse (warehouse schema)
    tables:
      - name: clients
        description: "Reference table for current clients, with various ID and service definition values"
        meta:
          dagster:
            asset_key: [ "clients" ]
