with base_events as (
    select
        id,
        create_date,
        client_id,
        event_date,
        creative_name,
        creative_slot,
        event_name,
        user_pseudo_id,
        case when user_pseudo_id is null then null else CONCAT(client_id, '-', user_pseudo_id) END AS ugcid,
        CASE
          WHEN user_pseudo_id IS NULL OR session_id IS NULL THEN NULL
          ELSE CONCAT(client_id, '-', user_pseudo_id, '-', session_id)
        END AS usid, -- user_pseudo_id comes from the gcid field
        user_pseudo_id as gcid,
        hostname,
        region,
        offer_set,
        order_monthly_total,
        order_total,
        org_id,
        package_change_type,
        page_location,
        page_title,
        page_url,
        promotion_id,
        promotion_name,
        session_id as original_ga_session_id,
        case when session_id REGEXP '^\\s*$' then null else session_id end as session_id,
        case when session_number REGEXP '^\\d+$' then session_number end AS session_number,
        shopper_id,
        shopper_type,
        language,
        browser_version,
        category,
        city,
        content,
        continent,
        country,
        default_channel_grouping,
        metro,
        mobile_brand_name,
        mobile_marketing_name,
        mobile_model_name,
        mobile_os_hardware_model,
        operating_system,
        operating_system_version,
        page_referrer,
        stream_id,
        sub_continent,
        term,
        traffic_source_campaign,
        traffic_source_medium,
        traffic_source_source,
        user_params_campaign,
        user_params_medium,
        case when user_params_source REGEXP '^\\s*$' then user_params_source end AS user_params_source,
        gclid,
        etl_source_name,
        order_id,
        credit_bureau,
        credit_check_type,
        credit_check_result,
        input_address,
        browser,
        app_load_id,
        buyflow_start_id,
        core_event_id,
        core_event_params_source,
        core_event_params_campaign,
        core_event_params_medium,
        core_event_params_term,
        core_event_params_content,
        unique_plan_id
    from {{ ref('all_events_base') }}
)

SELECT
    e.*,
    b.bf_id,
    b.bf_create_date,
    b.bf_usid,
    b.bf_ugcid,
    b.bf_gcid,
    b.bf_session_id,
    b.bf_buyflow_start_id
FROM base_events e
LEFT JOIN {{ ref('buyflow_starts') }} b ON e.app_load_id = b.app_load_id
