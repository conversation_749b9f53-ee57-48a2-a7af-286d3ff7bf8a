with

-- Take newly calculated buyflow_starts over original table stats if app_load_id exists
events_with_updated_starts AS (
    SELECT
        id,
        create_date,
        client_id,
        event_date,
        creative_name,
        creative_slot,
        event_name,
        user_pseudo_id,
        CASE WHEN bf_id IS NOT NULL THEN bf_gcid ELSE gcid END AS gcid,
        CASE WHEN bf_id IS NOT NULL THEN bf_ugcid ELSE ugcid END AS ugcid,
        CASE WHEN bf_id IS NOT NULL THEN bf_usid ELSE usid END AS usid,
        hostname,
        region,
        offer_set,
        order_monthly_total,
        order_total,
        org_id,
        package_change_type,
        page_location,
        page_title,
        page_url,
        promotion_id,
        promotion_name,
        original_ga_session_id,
        CASE WHEN bf_id IS NOT NULL THEN bf_session_id ELSE session_id END AS session_id,
        session_number,
        shopper_id,
        shopper_type,
        language,
        browser_version,
        category,
        city,
        content,
        continent,
        country,
        default_channel_grouping,
        metro,
        mobile_brand_name,
        mobile_marketing_name,
        mobile_model_name,
        mobile_os_hardware_model,
        operating_system,
        operating_system_version,
        page_referrer,
        stream_id,
        sub_continent,
        term,
        traffic_source_campaign,
        traffic_source_medium,
        traffic_source_source,
        CASE WHEN bf_id IS NOT NULL THEN app_load_id ELSE app_load_id END AS app_load_id,
        CASE WHEN bf_id IS NOT NULL THEN bf_buyflow_start_id ELSE buyflow_start_id END AS buyflow_start_id,
        user_params_campaign,
        user_params_medium,
        user_params_source,
        gclid,
        etl_source_name,
        order_id,
        credit_bureau,
        credit_check_type,
        credit_check_result,
        input_address,
        browser,
        core_event_id,
        core_event_params_source,
        core_event_params_campaign,
        core_event_params_medium,
        core_event_params_term,
        core_event_params_content,
        unique_plan_id
    FROM {{ ref('events_with_buyflow_starts') }}
)

-- Set Google Client fields over session, identify the first event with source/campaign/medium per ugcid

SELECT
    ugcid,
    MIN(CASE WHEN user_params_source IS NOT NULL OR user_params_campaign IS NOT NULL OR user_params_medium IS NOT NULL THEN id ELSE NULL END) AS gc_sid,
    MIN(CASE WHEN user_params_source IS NOT NULL OR user_params_campaign IS NOT NULL OR user_params_medium IS NOT NULL THEN create_date ELSE NULL END) AS gc_sid_createDate
FROM events_with_updated_starts
WHERE ugcid IS NOT NULL
GROUP BY ugcid
