-- Final processed events data
SELECT
  e.*,
  c.csm_source,
  c.csm_medium,
  c.csm_campaign,
  c.csm_gclid,
  -- Create a session ID that combines session_id and gcid, or falls back to app_load_id
  COALESCE(CONCAT(e.session_id, e.gcid), e.app_load_id) AS SessionID,
  -- Create lead capture and customize options session IDs
  CASE
    WHEN e.event_name = 'page_view' AND e.page_location LIKE '%leadCapture%'
    THEN COALESCE(CONCAT(e.session_id, e.gcid), e.app_load_id)
  END AS LeadCaptureSessionId,
  CASE
    WHEN e.event_name = 'page_view' AND e.page_location LIKE '%customizeOptions%'
    THEN COALESCE(CONCAT(e.session_id, e.gcid), e.app_load_id)
  END AS CustomizeOptionsSessionId
FROM {{ ref('events_with_unset_gc') }} e
LEFT JOIN {{ ref('csm_source_info') }} c ON e.csm_sid = c.csm_sid






