
version: 2

models:
  - name: all_events_base
    description: "Combines Core Events and GA Events into a single unified table"
    meta:
      dagster:
        group: "warehouse"
  - name: buyflow_starts
    description: "Identifies the first buyflow start event for each app load"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_buyflow_starts
    description: "Applies buyflow starts to all events with the same app_load_id"
    meta:
      dagster:
        group: "warehouse"
  - name: gc_first_events
    description: "Identifies the first event with source/campaign/medium per ugcid"
    meta:
      dagster:
        group: "warehouse"
  - name: gc_source_info
    description: "Joins back the source information to Google Client first events"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_gc_fields
    description: "Applies Google Client source information to all events with the same ugcid"
    meta:
      dagster:
        group: "warehouse"
  - name: buyflow_first_events
    description: "Identifies the first buyflow start event for each usid"
    meta:
      dagster:
        group: "warehouse"
  - name: buyflow_source_info
    description: "Joins back the source information to buyflow first events"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_buyflow_fields
    description: "Applies buyflow source information to all events with the same usid"
    meta:
      dagster:
        group: "warehouse"
  - name: pageview_first_events
    description: "Identifies the first page view event for each usid"
    meta:
      dagster:
        group: "warehouse"
  - name: pageview_source_info
    description: "Joins back the source information to pageview first events"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_pageview_fields
    description: "Applies pageview source information to all events with the same usid"
    meta:
      dagster:
        group: "warehouse"
  - name: session_start_dates
    description: "Determines the start date for each session"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_session_dates
    description: "Applies session start dates to all events with the same usid"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_csm_case
    description: "Sets the Campaign Source Medium case for each event"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_csm_sid
    description: "Sets the Campaign Source Medium SID for each event"
    meta:
      dagster:
        group: "warehouse"
  - name: events_with_unset_gc
    description: "Unsets Google Client fields for non-GC cases"
    meta:
      dagster:
        group: "warehouse"
  - name: csm_source_info
    description: "Joins back the source information to Campaign Source Medium first events"
    meta:
      dagster:
        group: "warehouse"
  - name: all_events_normalized
    description: "Filters/decorates/transforms records from both event sources into internally-consistent values"
    meta:
      dagster:
        group: "warehouse"

sources:
  - name: analytics
    schema: analytics
    description: Apache Doris lakehouse (analytics schema)
    tables:
      - name: ga_events_base
        meta:
          dagster:
            asset_key: [ "ga_events_base" ]

