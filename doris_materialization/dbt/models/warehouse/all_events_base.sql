{{ config(
    materialized='table',
    unique_key=['id'],
    partition_by="create_date",
    partition_type='range',
    partition_by_init=[
        "PARTITION p202306 VALUES LESS THAN ('2023-06-01')",
        "PARTITION p202307 VALUES LESS THAN ('2023-07-01')",
        "PARTITION p202308 VALUES LESS THAN ('2023-08-01')",
        "PARTITION p202309 VALUES LESS THAN ('2023-09-01')",
        "PARTITION p202310 VALUES LESS THAN ('2023-10-01')",
        "PARTITION p202311 VALUES LESS THAN ('2023-11-01')",
        "PARTITION p202312 VALUES LESS THAN ('2023-12-01')",
        "PARTITION p202401 VALUES LESS THAN ('2024-01-01')",
        "PARTITION p202402 VALUES LESS THAN ('2024-02-01')",
        "PARTITION p202403 VALUES LESS THAN ('2024-03-01')",
        "PARTITION p202404 VALUES LESS THAN ('2024-04-01')",
        "PARTITION p202405 VALUES LESS THAN ('2024-05-01')",
        "PARTITION p202406 VALUES LESS THAN ('2024-06-01')",
        "PARTITION p202407 VALUES LESS THAN ('2024-07-01')",
        "PARTITION p202408 VALUES LESS THAN ('2024-08-01')",
        "PARTITION p202409 VALUES LESS THAN ('2024-09-01')",
        "PARTITION p202410 VALUES LESS THAN ('2024-10-01')",
        "PARTITION p202411 VALUES LESS THAN ('2024-11-01')",
        "PARTITION p202412 VALUES LESS THAN ('2024-12-01')",
        "PARTITION p202501 VALUES LESS THAN ('2025-01-01')",
        "PARTITION p202502 VALUES LESS THAN ('2025-02-01')",
        "PARTITION p202503 VALUES LESS THAN ('2025-03-01')",
        "PARTITION p202504 VALUES LESS THAN ('2025-04-01')",
        "PARTITION p202505 VALUES LESS THAN ('2025-05-01')",
        "PARTITION p202506 VALUES LESS THAN ('2025-06-01')",
        "PARTITION p202507 VALUES LESS THAN ('2025-07-01')",
        "PARTITION p202508 VALUES LESS THAN ('2025-08-01')",
        "PARTITION p202509 VALUES LESS THAN ('2025-09-01')",
        "PARTITION p202510 VALUES LESS THAN ('2025-10-01')",
        "PARTITION p202511 VALUES LESS THAN ('2025-11-01')",
        "PARTITION p202512 VALUES LESS THAN ('2025-12-01')"
      ]
) }}


SELECT
    id as id,
    createDate as create_date,
    clientId as client_id,
    concat(SUBSTR(cast(createDate as string), 1, 4), SUBSTR(cast(createDate as string), 6, 2), SUBSTR(cast(createDate as string), 9, 2)) AS event_date,
    creative_name,
    creative_slot,
    event_name,
    gcid as user_pseudo_id,
    host as hostname,
    market as region,
    offer_set,
    order_monthly_total,
    order_total,
    orgId as org_id,
    package_change_type,
    page_location,
    page_title,
    page_url,
    promotion_id,
    promotion_name,
    session_id,
    session_number,
    shopperId as shopper_id,
    shopper_type,
    NULL AS `language`,
    NULL AS browser_version,
    NULL AS category,
    NULL AS city,
    NULL AS content,
    NULL AS continent,
    NULL AS country,
    '' AS default_channel_grouping,
    NULL AS metro,
    NULL AS mobile_brand_name,
    NULL AS mobile_marketing_name,
    NULL AS mobile_model_name,
    NULL AS mobile_os_hardware_model,
    NULL AS operating_system,
    NULL AS operating_system_version,
    NULL AS page_referrer,
    NULL AS stream_id,
    NULL AS sub_continent,
    NULL AS term,
    NULL AS traffic_source_campaign,
    NULL AS traffic_source_medium,
    NULL AS traffic_source_source,
    NULL AS user_params_campaign,
    NULL AS user_params_medium,
    NULL AS user_params_source,
    NULL AS gclid,
    'core_events' as etl_source_name,
    orderid as order_id,
    credit_bureau,
    credit_check_type,
    credit_check_result,
    input_address,
    NULL AS browser,
    app_load_id,
    buyflow_start_id,
    coreEventId as core_event_id,
    JSON_UNQUOTE(JSON_EXTRACT(event_params, '$.source')) AS core_event_params_source,
    JSON_UNQUOTE(JSON_EXTRACT(event_params, '$.campaign')) AS core_event_params_campaign,
    JSON_UNQUOTE(JSON_EXTRACT(event_params, '$.medium')) AS core_event_params_medium,
    JSON_UNQUOTE(JSON_EXTRACT(event_params, '$.term')) AS core_event_params_term,
    JSON_UNQUOTE(JSON_EXTRACT(event_params, '$.content')) AS core_event_params_content,
    unique_plan_id

from {{ ref("core_events_base") }}
where event_params is not null

UNION ALL

SELECT
    id as id,
    created_date as create_date,
    client_id,
    event_date,
    NULL AS creative_name,
    NULL AS creative_slot,
    event_name,
    user_pseudo_id,
    hostname,
    region,
    NULL AS offer_set,
    NULL AS order_monthly_total,
    NULL AS order_total,
    org_id,
    NULL AS package_change_type,
    page_location,
    page_title,
    Null as page_url,
    NULL AS promotion_id,
    NULL AS promotion_name,
    cast(ga_session_id as string) as session_id,
    cast(ga_session_number as string) as session_number,
    NULL AS shopper_id,
    NULL AS shopper_type,
    `language`,
    browser_version,
    category,
    city,
    content,
    continent,
    country,
    '' as default_channel_grouping,
    metro,
    mobile_brand_name,
    mobile_marketing_name,
    mobile_model_name,
    mobile_os_hardware_model,
    operating_system,
    operating_system_version,
    page_referrer,
    stream_id,
    sub_continent,
    term,
    traffic_source_name as traffic_source_campaign,
    traffic_source_medium,
    traffic_source_source,
    user_params_campaign,
    user_params_medium,
    user_params_source,
    gclid,
    'client_events' as etl_source_name,
    NULL AS order_id,
    NULL AS credit_bureau,
    NULL AS credit_check_type,
    NULL AS credit_check_result,
    NULL AS input_address,
    browser,
    app_load_id,
    buyflow_start_id,
    NULL AS core_event_id,
    NULL AS core_event_params_source,
    NULL AS core_event_params_campaign,
    NULL AS core_event_params_medium,
    NULL AS core_event_params_term,
    NULL AS core_event_params_content,
    NULL AS unique_plan_id
from {{ source('analytics','ga_events_base') }}


