
version: 2

models:
  - name: ads_campaign_raw
    description: "Incrementally updated ads/campaign data from Google Ads"
    meta:
      dagster:
        group: "ads"

sources:
  - name: ads
    schema: ads
    description: Apache Doris lakehouse (ads schema)
    tables:
      - name: channel_group_sources
        description: "Reference table for applying categories to different advertiser origins"
        meta:
          dagster:
            asset_key: [ "channel_group_sources" ]
      - name: bq_ads_info_schema
        freshness: null
        meta:
          dagster:
            asset_key: ["bq_ads_info_schema"]
