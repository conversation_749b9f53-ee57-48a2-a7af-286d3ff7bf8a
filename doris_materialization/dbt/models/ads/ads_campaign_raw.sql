{% set q_ads_sources %}
    with
    clickstats_tables as (
        select
            table_name,
            database_name
        from {{ source('ads','bq_ads_info_schema') }}
        where catalog_name = 'atlas_bigquery_prd'
        and table_name like 'p_ads_clickstats%'
    ),
    campaign_tables as (
        select
            table_name,
            database_name
        from {{ source('ads','bq_ads_info_schema') }}
        where catalog_name = 'atlas_bigquery_prd'
        and table_name like 'p_ads_campaign%'
    )
    select
        split_part(cl.database_name, '_', 2) as client_id,
        cl.table_name as clickstats_table,
        ca.table_name as campaign_table
    from clickstats_tables cl
    join campaign_tables ca on ca.database_name = cl.database_name;
{% endset %}

{% set ads_sources = run_query(q_ads_sources) %}
{% if execute %}
    {% set data_ads_sources = ads_sources.data %}
    {% for ads_source in data_ads_sources %}
        select * from (
            WITH a as (
                SELECT * FROM {{ ads_source[1] }}.{{ ads_source[2] }} -- clickstats
            ),
            b as (
                SELECT
                    campaign_id,
                    campaign_name
                FROM (
                    SELECT
                        campaign_id,
                        campaign_name,
                        ROW_NUMBER() OVER(PARTITION BY campaign_id ORDER BY _PARTITIONTIME desc) AS first_created
                    FROM {{ ads_source[1] }}.{{ ads_source[3] }} -- campaign
                )
                WHERE first_created = 1
            )
            -- Select the most recent campaign table row based on _PARTITIONTIME
            select
                '{{ ads_source[0] }}' as client_id,
                '{{ ads_source[2] }}' as bq_source_clickstats_table,
                '{{ ads_source[3] }}' as bq_source_campaign_table
                a.click_view_gclid,
                a.click_view_keyword,
                b.*
            from  a
            join  b on a.campaign_id = b.campaign_id
        )

    {% if not loop.last %}
    union all
    {% endif %}
    {% endfor %}
{% endif %}