-- depends on {{ source('analytics','ga_events_dated_snapshot') }}

{% set ga_event_date = var("ga_event_date", "") %}
{% if ga_event_date == "" %}

    {# Get the most recent date for intraday exports in the remote catalog #}
    {% set q_recent_analytics_date %}
        with analytics_dates as
         (
            select split_part(table_name, '_', 3) as event_date
            from {{ source('analytics','bq_analytics_info_schema') }}
            where catalog_name = 'atlas_bigquery_prd'
        )
        select max(event_date) as recent_date from analytics_dates
        where event_date REGEXP '^20[0-9]+$'
        and length(event_date) < 9;
    {% endset %}

    {% set recent_analytics_dates = run_query(q_recent_analytics_date) %}
    {% if execute %}
        {% set recent_analytics_date = recent_analytics_dates.columns[0].values()[0] %}
    {% else %}
        {% set recent_analytics_date = '' %}
    {% endif %}
{% else %}
    {% set recent_analytics_date = ga_event_date %}
{% endif %}

{# Get all snapshot tables for the current ga_event_date #}
{% set q_snapshot_tables %}
SHOW TABLES from analytics LIKE 'ga_events_snapshot_{{ recent_analytics_date }}_%';
{% endset %}

{% set snapshot_tables = run_query(q_snapshot_tables) %}
-- executing snapshot_tables query {{ q_snapshot_tables | replace('\n', '') }}
-- results: {{ snapshot_tables.columns[0].values() | join(', ') }}

{% if execute %}
    {% set list_snapshot_tables = snapshot_tables.columns[0].values() %}
{% else %}
    -- setting empty snapshot_tables list due to execute=false
    {% set list_snapshot_tables = [] %}
{% endif %}

{# See if the final target table has been materialized and properly named. If so, add it to the snapshot sources #}
{% set q_final_table %}
SHOW TABLES from analytics LIKE 'ga_events_{{ recent_analytics_date }}';
{% endset %}


{% set final_table = run_query(q_final_table) %}
-- executing final_table query {{ final_table | replace('\n', '') }}
-- results: {{ final_table.columns[0].values() | join(', ') }}
{% if execute %}
    {% set list_snapshot_tables = final_table.columns[0].values() %}
{% endif %}


{% for snapshot_table in list_snapshot_tables %}
select
    *
from analytics.{{ snapshot_table }}
{% if not loop.last %}
union
{% endif %}
{% endfor %}
