{% macro doris__duplicate_key() -%}
  {% set cols = config.get('duplicate_key', validator=validation.any[list, basestring]) %}
  {% if cols %}
      {%- if cols is string -%}
        {%- set cols = [cols] -%}
      {%- endif -%}
    DUPLICATE KEY (
      {% for item in cols %}
        {{ item }}{% if not loop.last %},{% endif %}
      {% endfor %}
    )
  {% endif %}
{%- endmacro %}

{% macro doris__unique_key() -%}
  {% set cols = config.get('unique_key', validator=validation.any[list, basestring]) %}
  {% if cols %}
      {%- if cols is string -%}
        {%- set cols = [cols] -%}
      {%- endif -%}
    UNIQUE KEY (
      {% for item in cols %}
        {{ item }}{% if not loop.last %},{% endif %}
      {% endfor %}
    )
  {% endif %}
{%- endmacro %}

{% macro doris__table_comment() -%}
  {% set comment = config.get('comment', validator=validation.any[basestring]) %}
  {% if comment %}
    COMMENT "{{ comment }}"
  {% endif %}
{%- endmacro %}

{% macro doris__partition_by() -%}
  {% set partition_by = config.get('partition_by', validator=validation.any[basestring]) %}
  {% set partition_type = config.get('partition_type', validator=validation.any[basestring]) %}
  {% set partition_by_init = config.get('partition_by_init', validator=validation.any[list]) %}
  
  {% if partition_by %}
    PARTITION BY {{ partition_type or 'RANGE' }} ({{ partition_by }})
    {% if partition_by_init %}
    (
      {% for item in partition_by_init %}
        {{ item }}{% if not loop.last %},{% endif %}
      {% endfor %}
    )
    {% endif %}
  {% endif %}
{%- endmacro %}

{% macro doris__distributed_by() -%}
  {% set cols = config.get('distributed_by', validator=validation.any[list, basestring]) %}
  {% if cols %}
      {%- if cols is string -%}
        {%- set cols = [cols] -%}
      {%- endif -%}
    DISTRIBUTED BY HASH (
      {% for item in cols %}
        {{ item }}{% if not loop.last %},{% endif %}
      {% endfor %}
    ) BUCKETS {{ config.get('buckets', validator=validation.any[int]) or 10 }}
  {% endif %}
{%- endmacro %}

{% macro doris__properties() -%}
  {% set properties = config.get('properties', validator=validation.any[dict]) %}
  {% set replice_num =  config.get('replication_num') %}

  {% if replice_num is not none %}
    {% if properties is none %}
      {% set properties = {} %}
    {% endif %}
    {% do properties.update({'replication_num': replice_num}) %}
  {% endif %}

  {% if properties is not none %}
    PROPERTIES (
        {% for key, value in properties.items() %}
          "{{ key }}" = "{{ value }}"{% if not loop.last %},{% endif %}
        {% endfor %}
    )
  {% endif %}
{%- endmacro%}

{% macro doris__create_table_as(temporary, relation, sql) -%}
    {% set sql_header = config.get('sql_header', none) %}
    {% set table = relation.include(database=False) %}
    {{ sql_header if sql_header is not none }}
    {%if temporary %}
        {{doris__drop_relation(relation)}}
    {% endif %}
    create table {{ table }}
    {{ doris__duplicate_key() }}
    {{ doris__table_comment()}}
    {{ doris__partition_by() }}
    {{ doris__distributed_by() }}
    {{ doris__properties() }} as {{ doris__table_colume_type(sql) }};
{%- endmacro %}

{% macro doris__table_colume_type(sql) -%}
    {% set cols = model.get('columns') %}
    {% if cols %}
        select {{get_table_columns_and_constraints()}} from (
            {{sql}}
        ) `_table_colume_type_name`
    {% else %}
        {{sql}}
    {%- endif -%}
{%- endmacro %}
