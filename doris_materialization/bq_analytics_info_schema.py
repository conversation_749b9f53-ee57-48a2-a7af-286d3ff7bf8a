import pandas as pd
from dagster import asset, OpExecutionContext
import pymysql
import fnmatch
from doris_materialization.lib.config import doris_host, doris_port, doris_user, doris_password, analytics_database


def list_catalogs(cursor):
    """Retrieve all available catalogs"""
    cursor.execute("SHOW CATALOGS;")
    return [row[1] for row in cursor.fetchall()]

def list_databases(cursor, catalog):
    """Retrieve all databases from a given catalog"""
    cursor.execute(f"SHOW DATABASES FROM {catalog};")
    return [row[0] for row in cursor.fetchall()]

def list_tables(cursor, catalog, database):
    """Retrieve all tables from a given catalog and database"""
    cursor.execute(f"SHOW TABLES FROM {catalog}.{database};")
    return [row[0] for row in cursor.fetchall()]

def get_doris_tables(conn, filter_databases = None, filter_catalogs = None, filter_tables = None):
    cursor = conn.cursor()
    all_tables = []

    try:
        catalogs = list_catalogs(cursor)
        for catalog in catalogs:
            if (filter_catalogs is not None and any(fnmatch.fnmatch(catalog, pattern) for pattern in filter_catalogs)) or (filter_catalogs is None):
                databases = list_databases(cursor, catalog)
                for db in databases:
                    if (filter_databases is not None and any(fnmatch.fnmatch(db, pattern) for pattern in filter_databases)) or (filter_databases is None):
                        tables = list_tables(cursor, catalog, db)
                        for table in tables:
                            if (filter_tables is not None and any(fnmatch.fnmatch(table, pattern) for pattern in filter_tables)) or (filter_tables is None):
                                table_row = {'catalog_name': catalog, 'database_name': db, 'table_name': table}
                                all_tables.append(table_row)
                                # print(table_row)
    finally:
        cursor.close()
    #     conn.close()

    return all_tables


@asset(compute_kind='python', group_name="analytics")
def bq_analytics_info_schema(context: OpExecutionContext):

    # Connect to Doris
    context.log.info(f"Establishing connection to Doris host {doris_host}")
    conn = pymysql.connect(
        host=doris_host,
        port=doris_port,
        user=doris_user,
        password=doris_password,
        database=analytics_database
    )

    # Create the table if it doesn't exist
    drop_table_sql = """
    DROP TABLE IF EXISTS bq_analytics_info_schema
    """

    create_table_sql = """
    CREATE TABLE IF NOT EXISTS bq_analytics_info_schema (
        catalog_name VARCHAR(255),
        database_name VARCHAR(255),
        table_name VARCHAR(255)
    )
    """
    context.log.info(f"Dropping and re-initializing `bq_analytics_info_schema` table")
    with conn.cursor() as cursor:
        cursor.execute(drop_table_sql)
        cursor.execute(create_table_sql)
    conn.commit()

    context.log.info(f"Capturing catalogs/databases/tables from remote BigQuery catalogs")
    df_doris_tables = pd.DataFrame(get_doris_tables(conn, filter_catalogs = ['atlas_bigquery*'], filter_databases = ['analytics_?????????','analytics_??????????'], filter_tables = ['events_intraday_*']))

    # Insert data from DataFrame to Doris
    context.log.info(f"Inserting table records into `bq_analytics_info_schema` table")
    data_to_insert = [tuple(x) for x in df_doris_tables.values]
    insert_sql = "INSERT INTO bq_analytics_info_schema VALUES (%s, %s, %s)" # No need to specify database here
    with conn.cursor() as cursor:
        cursor.executemany(insert_sql, data_to_insert)
    conn.commit()
    conn.close()

    return f"{analytics_database}.bq_analytics_info_schema"

