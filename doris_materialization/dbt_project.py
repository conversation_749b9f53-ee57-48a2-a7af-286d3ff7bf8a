from dagster_dbt import dbt_assets, DbtCliResource, DbtProject, DagsterDbtTranslator, DagsterDbtTranslatorSettings
import dagster as dg
import json
from doris_materialization.lib.config import dbt_path

dbt_project = DbtProject(project_dir=dbt_path)
dbt_project.prepare_if_dev()

# links to dbt model source code from assets
dagster_dbt_translator = DagsterDbtTranslator(
    settings=DagsterDbtTranslatorSettings(enable_code_references=True)
)

class DbtProjectConfig(dg.Config):
    target: str = 'doris'
    vars: dict = {}

@dbt_assets(
    manifest=dbt_project.manifest_path,
    dagster_dbt_translator = dagster_dbt_translator,
    project = dbt_project,
)
def dbt_project(context: dg.AssetExecutionContext, dbt: DbtCliResource, config: DbtProjectConfig):
    dg.get_dagster_logger().info(f"Running dbt with target: {config.target}, vars: {config.vars}")

    # clean out old temp files before executing
    dbt.cli(["clean"])  # Invoke dbt clean command

    # define the command line arguments to pass to dbt
    dbt_args = ["build", "--target", config.target]

    # pull the variables dict from the config object and format for dbt command line
    vars_json = json.dumps(config.vars)
    dbt_args.extend(["--vars", f"{vars_json}"])

    yield from dbt.cli(dbt_args, context=context).stream()
