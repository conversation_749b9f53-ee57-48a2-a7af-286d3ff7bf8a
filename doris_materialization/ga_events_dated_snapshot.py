from dagster import asset, OpExecutionContext, AssetKey
import pymysql
from doris_materialization.lib.config import doris_host, doris_port, doris_user, doris_password, analytics_database
from datetime import datetime

@asset(
    compute_kind='python',
    deps=[Asset<PERSON>ey('latest_intraday_events_snapshot')],
    group_name="analytics",
    description="Applies a date-specific temporary table name to freshly-ingested GA data"
)
def ga_events_dated_snapshot(context: OpExecutionContext):

    # Connect to Doris
    context.log.info(f"Establishing connection to Doris host {doris_host}")
    conn = pymysql.connect(
        host=doris_host,
        port=doris_port,
        user=doris_user,
        password=doris_password,
        database=analytics_database
    )

    # retrieve the event date in the latest intraday events table (if exists)
    event_date = None
    new_table_name = None

    try:
        # Create a cursor object
        with conn.cursor() as cursor:
            sql = "select max(event_date) as recent_date from latest_intraday_events_snapshot"
            cursor.execute(sql)
            row = cursor.fetchone()
            if row is not None:
                event_date = row[0]
                context.log.info(f"Current date value in latest_intraday_events_snapshot is to `{event_date}`")

    except Exception as err:
        context.log.error(err)

    if event_date:
        now = datetime.now()
        snapshot_time = now.strftime("%H%M%S")
        new_table_name = f"ga_events_snapshot_{event_date}_{snapshot_time}"
        rename_table_sql = f"""
            use analytics; 
            drop table if exists {new_table_name};
            alter table latest_intraday_events_snapshot rename {new_table_name};
        """
        context.log.debug(rename_table_sql)
        context.log.info(f"Renaming current analytics table `latest_intraday_events_snapshot` to `{new_table_name}`")
        with conn.cursor() as cursor:
            cursor.execute(rename_table_sql)
        conn.commit()

    return new_table_name
