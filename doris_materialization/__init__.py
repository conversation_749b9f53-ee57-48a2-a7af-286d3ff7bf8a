from doris_materialization.lib.ga_ingest_config import GoogleAnalyticsIngestConfig, GA_INGEST_CONFIG
from doris_materialization.lib.config import definition_path, dag_path, dbt_path

import dagster as dg
from dagster_dbt import DbtCliResource

from doris_materialization import (
    dbt_project,
    bq_analytics_info_schema,
    bq_ads_info_schema,
    ga_events_dated_final,
    ga_events_dated_snapshot,
    prune_old_files,
    clients,
    ga_events_base,
)

# initialize our imported objects that include dagster-decorated functions
all_assets = dg.load_assets_from_modules([
    dbt_project,
    clients,
    bq_analytics_info_schema,
    bq_ads_info_schema,
    ga_events_dated_final,
    ga_events_dated_snapshot,
    ga_events_base,
])

# define job for Latest (the main job)
latest_selection = [
    'analytics/latest_intraday_events_raw',
    'analytics/latest_intraday_events_aggregate',
    'analytics/latest_intraday_events_snapshot',
    'warehouse/all_events_base',
    'warehouse/buyflow_starts',
    'warehouse/events_with_buyflow_starts',
    'warehouse/gc_first_events',
    'warehouse/gc_source_info',
    'warehouse/events_with_gc_fields',
    'warehouse/buyflow_first_events',
    'warehouse/buyflow_source_info',
    'warehouse/events_with_buyflow_fields',
    'warehouse/pageview_first_events',
    'warehouse/pageview_source_info',
    'warehouse/events_with_pageview_fields',
    'warehouse/session_start_dates',
    'warehouse/events_with_session_dates',
    'warehouse/events_with_csm_case',
    'warehouse/events_with_csm_sid',
    'warehouse/events_with_unset_gc',
    'warehouse/csm_source_info',
    'warehouse/all_events_normalized',
    'core/core_events_raw',
    'core/core_events_base',
    'clients',
    'ga_events_dated_snapshot',
    'ga_events_dated_final',
    'bq_analytics_info_schema',
    'bq_ads_info_schema',
    'ga_events_base',
    'ads/ads_campaign_raw',
]
latest = dg.define_asset_job(
    name="Latest",
    description="Imports and prepares reportable data from raw sources",
    selection=dg.AssetSelection.assets(*latest_selection),
    config={
        "ops": {
            "ga_events_base": {
                "config": {
                    "reload_days": 15,
                    "source_batch_size": 15
                },
            }
        }
    }
)

# define job for historic GA ingest
historic_ga_events_selection = [
    'analytics/latest_intraday_events_raw',
    'analytics/latest_intraday_events_aggregate',
    'analytics/latest_intraday_events_snapshot',
    'ga_events_dated_snapshot',
    'ga_events_dated_final'
]

from dagster import job, op, ConfigMapping, Config


historic_ga_events = dg.define_asset_job(
    name="Historic_GA_Events",
    description="Imports a single day of Google Analytics events from all raw client sources.",
    selection=dg.AssetSelection.assets(*historic_ga_events_selection),
    config={
        "ops": {
            "dbt_project": {
                "config": {
                    "vars": {"ga_event_date": ""},
                }
            },
            "dbt_project_2": {
                "config": {
                    "vars": {"ga_event_date": ""},
                }
            },
        }
    }
)

# define a job for core events ingest only
historic_core_events_selection = [
    'core/core_events_raw'
]
historic_core_events = dg.define_asset_job(
    name="Historic_Core_Events",
    description="Imports up to 1M of the most recent core events records from Atlas Core prod Postgres",
    selection=dg.AssetSelection.assets(*historic_core_events_selection),
)


# Define a blank list for us to accumulate graph component definitions in
definitions_list = []

# Add dbt as a generic resource, we only need to do this one time for anything that uses dbt
definitions_list.append(
    dg.Definitions(
        resources={
            f"dbt": DbtCliResource(project_dir=dbt_path)
        }
    )
)

# add our static assets, jobs, and sensors
definitions_list.append(dg.Definitions(
    assets=all_assets,
    jobs=[latest,historic_ga_events,historic_core_events],
    sensors=[prune_old_files.prune_old_files_sensor]
))

# produce the reserved defs object by merging all elements of our definitions list into a single definition object
defs = dg.Definitions.merge(*definitions_list)


# Uncomment below to run with Pycharm IDE debugger
# result = defs.get_job_def('Historic_Events').execute_in_process()
# print(result)
