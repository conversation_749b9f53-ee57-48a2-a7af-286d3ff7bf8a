import pandas as pd
from dagster import asset, OpExecutionContext
import pymysql
from doris_materialization.lib.config import doris_host, doris_port, doris_user, doris_password
from doris_materialization.lib.legacy_clientinfo import getAllClientsAsDataFrame


def update_clients_table(conn, clients_df: pd.DataFrame):

    # Create the table if it doesn't exist
    drop_table_sql = """
    DROP TABLE IF EXISTS clients
    """

    create_table_sql = """
    CREATE TABLE IF NOT EXISTS clients (
        client_id VARCHAR(255),
        org_id VARCHAR(255),
        ga4_id VARCHAR(255),
        measurement_id VARCHAR(255),
        dns VARCHAR(255),
        ads_id VARCHAR(255),
        bq_dataset_base VARCHAR(255),
        is_active VARCHAR(255)
    )
    """
    with conn.cursor() as cursor:
        cursor.execute(drop_table_sql)
        cursor.execute(create_table_sql)
    conn.commit()

    # Insert data from DataFrame to Doris
    data_to_insert = [tuple(x) for x in clients_df.values]
    insert_sql = "INSERT INTO clients VALUES (%s, %s, %s, %s, %s, %s, %s, %s)" # No need to specify database here
    with conn.cursor() as cursor:
        cursor.executemany(insert_sql, data_to_insert)
    conn.commit()
    return "Data inserted successfully!"


@asset(
    compute_kind='python',
    group_name='warehouse',
    description='Loads client details from legacy configuration file into data warehouse reference table'
)
def clients(context: OpExecutionContext):

    target_database = 'warehouse'

    # Connect to Doris
    context.log.info(f"Establishing connection to Doris host {doris_host}")
    conn = pymysql.connect(
        host=doris_host,
        port=doris_port,
        user=doris_user,
        password=doris_password,
        database=target_database
    )

    clients_df = getAllClientsAsDataFrame()
    clients_df.columns = ['client_id', 'org_id', 'ga4_id', 'measurement_id', 'dns', 'ads_id', 'bq_dataset_base', 'is_active']
    update_clients_table(conn, clients_df)
    conn.close()

    return f"{target_database}.clients"

