# Pipeline Container Build & Deployment

This directory contains scripts and configuration files to deploy this project's code and supporting container into a single-node virtual machine running the [*k3s* Kubernetes distribution](https://k3s.io/). This deployment strategy is intended as an intermediate step between the legacy tooling—running templated shell scripts to initialize the container with Dock<PERSON>—and our ultimate goal of fully declarative, software-defined infrstructure using managed, multi-node Kubernetes clusters. 

This has been implemented in such a way that when we take that next step, much of the tooling & configuration found here will, with very minor adjustments, still be usable in managing & deploying the project.

Meanwhile, by running this container in a lightweight Kubernetes environment, much of the complicated scaffolding and templating found in the [Dagnote repository](https://github.com/atlas-digital-group/dagnote) is rendered moot. Like Docker, k8s allows multiple instances of dagster-based data pipelines (or any other workload) to share the computing and networking resources of the existing Dagster host machines. In addition, conveniences such as fault-tolerance, configuration & secrets management, web front-end proxying, security, performance monitoring, etc. are handled with documented, standard declarative APIs instead of one-off, opaque scripts and templates. Finally, k3s will peacefully coexist with other workloads on the same host still being managed by Docker, such as Dagnote. 

## Using these tools

You will find a `Makefile` in this directory that executes a number of scripts (in the `scripts` directory, directed by settings in the `config` directory) that handle tasks to make the container image and code accessible to the Kubernetes environment. Other scripts will initialize that environment with support services (using Terraform plans in the `terraform` directory,) and then launch the project container and mount this project directory at the expected container path, in the same way `docker-compose.yml` does when in local development. 

You can get a list of possible commands by opening this directory with a terminal and using the `make` command.  This will describe all options, which consist of:

### Container Building Commands

- `make build`  - Build the container for local infrastructure, no caching of layers.
- `make build-quick`  - Build the container for local infrastructure, with caching.
-  `make build-prod`  - Build the container for production infrastructure.
- `make push-prod`  - Push the last-built production container image to the designated container registry.
- `make build-push-prod`  - Build the container for production infrastructure and immediately push it to the registry.
- `make docker-auth`  - Sign into the designated container registry.

### Container Testing/Execution Commands

- `make start`  - Run the container for local infrastructure in the background, building first if necessary.
- `make stop`  - Terminate the instance of the container running in the background.
- `make connect`  - Open a console to the instance of the container running in the background.
- `make start-foreground`  - Run the container for local infrastructure in the foreground, building first if necessary.

### Declarative Infrastructure Commands

-  `make tf-init`  - Install Terraform executable into a local .venv in this directory.
-  `make tf-start {plan}`  - Deploy one of the terraform plans found in the `terraform` directory.
-  `make tf-stop {plan}`  - Terminate one of the terraform plans on the target system
-  `make tf-plan {plan}`  - Review target state, show proposed deployment steps for a given plan.
-  `make k8s-status`  - Get the current state of the selected Kubernetes cluster.
-  `make k8s-select`  - Choose which cluster configuration to work with. Defaults to `local` if not set. Wil also be run for any commands that require cluster-specific configuration, such as the `tf` commands. 
- *Terraform Plans* - Because Terraform is [idempotent](https://en.wikipedia.org/wiki/Idempotence), you can run these commands at any time, regardless of the current installation state. If anything is out of place, it will be reset according to the specification. In practice, you shouldn't need to run most of these once the k3s cluster is set up.  If you do, the order to run them in is:
  - `make tf-{action} cert-manager` - Installs `cert-manager`, which will register SSL certificates for http services hosted in the cluster.  
  - `make tf-{action} cert-manager-config` - Run after cert-manager installation, this defines the certificate valuidation strategy it will use. We will be using the `dns-01` strategy with a subdomain in AWS Route 53.
  - `make tf-{action} ecr-secret-operator ` - Installs a tool that will automatically retrieve a valid authentication token for the k8s environment to pull images from a private AWS Elastic Container Registry. 
  - `make tf-{action} project` - Installs the project files as a persistent volume pointed to the server's local filesystem, and launches the most recently built version of the docker image in the cluster, with appropriate web proxying to access the pipeline's user interface. 

## Host Initialization

In order for this all to work, we're assuming some prerequisites in technology for our server. It's assumed the server is running a recent version of Ubuntu linux, and is running on an amd64/x86 architecture.  We have to install a few things and manually store some configuration files before we can begin the real fun.  Sorry in advance. 

### Installing make & other basic support

The helper scripts (in the `script` directory) are all launched by the ancient and venerable `make` utility, which is controlled by the `Makefile` found at the root of `infrastructure`. On a fresh Linux machine, you may have to install it with this command: 

- `sudo apt install make` 

There are a handful of other miscellaneous tools used to manage terraform and python virtual environment, which also might not be installed. Run the commands: 

- `sudo apt install python3 python3-pip python3-venv`
- `sudo apt install jq`
- `sudo apt install unzip`

### Installing k3s

The installation for our standalone Kubernetes cluster is actually pretty simple. The details ([here](https://docs.k3s.io/quick-start)) can fill you in on the finer points, but the basic process is:

Turn off the server's memory-swapping feature, which does not play nice with declarative infrastructure that counts on a fixed amount of RAM being available: 

Run the command `sudo swapoff -a`, then, permanently turn off the feature by editing `/etc/fstab` and commenting out the `/dev/mapper/vg0-swap_1 none swap sw 0 0` line with a `#`.  

Next, install k3s with `curl -sfL https://get.k3s.io | sh -` and, after it completes, verify the service is up with `sudo systemctl status k3s`.  Finally, confirm the kubernetes cluster's admin tools work with `sudo k3s kubectl get nodes`.  

### Initial AWS CLI Setup

A few of the contaier-build processes are going to interact with Amazon Web Services in a way that demands software be installed and user settings be defined.  We do this so we can issue commands such as `aws ecr --profile ${aws_cli_profile} --region ${aws_region}  create-repository --repository-name ${prod_image_repo}` that nobody wants to have to type into a console. 

#### Install AWS command-line tools

Your host machine will need to be able to talk with AWS to store container images and define domain name resources. This is done through the `aws` command that comes with the [AWS Command Line Interface](https://aws.amazon.com/cli/). Follow the installation instructions [here](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-getting-started.html) for specific details, but the basic process on a Mac is:

``` bash 
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /
```

After this process completes, verify the `aws` command is properly installed by running the command `aws --version` - which should return something like `aws-cli/2.19.1 Python/3.11.6 Darwin/23.3.0 botocore/2.4.5`

#### Define an AWS profile with valid Access and Secret

After the AWS CLI tools are installed, you'll need to define a profile for interacting with the Atlas infrastructure. In the config files, the default named AWS profile is `atlas` and is defined in `config/aws/cli_profile`. 

Get an appropriately-privileged Access key and Secret key, and edit (or create) the credentials file in your home directory, at  `~/.aws/credentials`. Add a block to the file like so: 

```[atlas]
[atlas]
aws_access_key_id = AKIA0000000000000000
aws_secret_access_key = xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

Combined with the `--profile` option, the `aws` command in the helper scripts in this repo will now make use of these credentials.  

### Terraform Initialization

Finally, we need to install Terraform.  We'll do this inside this local directory, using a python `.venv` directory to host it.  And, while the earlier steps are very manual owing to the likelihood of variations in the host machine, this is entirely locally defined and thus, we have a helper command in `make` to do it for us.  Run the command `make tf-init` to establish the .venv and load the proper executables. Make sure you activate the .venv with `source ./.venv/bin/activate` so the scripts can see the command. 

## How Configuration Works

The `config` directory contains a lot of tiny text files, grouped into directories according to what they define. Most files will contain a single line of text—*no carriage return at the end*—that informs the behavior of one (or several) of the `make` actions listed above. 

### Config File Override Levels

There are two levels to configuration files. 

The first is what defines **generic, default, or local-development** values.  These are at the root of the config folder, and define stuff like the Docker registry we should be pushing to, how Terraform is configured, etc. There are a number of placeholder files with bogus values here, though, that serve as a guide to the **cluster-specific** values that are found in the `_clusters` directory. 

This repo is built to eventually accommodate deploying the same project to multiple Kubernetes clusters. This is done by having multiple folders in the `_clusters` directory. You can choose which cluster you want to deploy to by editing the `config/_clusters/selection` file. Within each directory in`_clusters`, you'll find a partial copy of the folders in the root `config` directory.  If a config file for a specific setting is found in the selected cluster config directory, it will be used instead of the generic value in the root config. 

Before you start working with a fresh pull of this repo, though, there will be a number of files that won't be there, since they'd contain credentials or other sensitive data.  We'll have to supply those ourselves, using the instructions below.

### Config files you'll need to supply on your own

Beyond accommodating the `aws` command, we will also need to supply credentials in the `config` directory, so we can deploy our application into the Kubernetes cluster. These files are in `.gitignore` so they won't be committed into the repo, nor be supplied for you on a fresh pull:

- `config/aws/access-key` - This is an AWS IAM key that is attached to privileges that can make updates to Route 53 domains and download container images from Elastic Container Registry. The same one we set up under the `atlas` profile for AWS CLI will work here. 
- `config/aws/access-secret` - The corresponding secret to the access key. 
- `config/aws/account-id` - the numeric ID of the Atlas AWS account that the key and secret are attached to. 
- `config/clusters/{local-cluster-name}/_k8s/kubeconfig` - A YAML-formatted file containing instructions for Terraform and the `kubectl` command to define resources in our local Kubernetes cluster. `kubectl` was installed when we installed k3s. On a k3s installation, a copy of the config file can be found at `/etc/rancher/k3s/k3s.yaml`.  