#!/bin/bash

source "$(dirname "$0")/common.sh"

app_deployment_name=$(set_local_config "project" "app-deployment-name")
namespace=$(set_local_config "project" "kubernetes-namespace")

# Get the pod name based on the app deployment name
pod_name=$(kubectl get pods -n "$namespace" -l "app=$app_deployment_name" -o jsonpath='{.items[0].metadata.name}')

# Check if a pod was found
if [ -z "$pod_name" ]; then
  echo "Error: No pod found for app deployment '$app_deployment_name' in namespace '$namespace'"
  exit 1
fi

# Open a shell to the container in the pod
kubectl exec -it -n "$namespace" "$pod_name" -- /bin/bash

# Exit the script
exit 0
