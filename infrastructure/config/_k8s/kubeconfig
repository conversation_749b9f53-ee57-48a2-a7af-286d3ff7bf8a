apiVersion: v1
kind: Config
clusters:
- name: "dummy-kubernetes-cluster-config-get-kubeconfig-from-cluster-and-save-in-_clusters-folder"
  cluster:
    server: "https://my-k8s-server-api-endpoint"
    certificate-authority-data: "boogedy boogedy booedy"

users:
- name: "andy"
  user:
    token: "kubeconfig-user-6hmqvk5lz7:7jhgnxkq7pnmb8jdhmvkhjsk9qc6b99xl48wrmd55rsvrl8s6b2rhd"


contexts:
- name: "foobar"
  context:
    user: "foo"
    cluster: "dummy-kubernetes-cluster-get-kubeconfig-from-cluster

current-context: "foobar"
