{"version": 4, "terraform_version": "1.11.3", "serial": 34, "lineage": "11d7088e-d1c2-1e52-6535-b766ee75256b", "outputs": {}, "resources": [{"mode": "managed", "type": "aws_route53_record", "name": "project_admin_dns_record", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"alias": [], "allow_overwrite": null, "cidr_routing_policy": [], "failover_routing_policy": [], "fqdn": "dagster-datamart.atlas.local.stl.waschick.org", "geolocation_routing_policy": [], "geoproximity_routing_policy": [], "health_check_id": "", "id": "Z0179074ADDMBNZ4MDD4_dagster-datamart.atlas.local.stl.waschick.org_A", "latency_routing_policy": [], "multivalue_answer_routing_policy": false, "name": "dagster-datamart.atlas.local.stl.waschick.org", "records": ["10.40.50.68"], "set_identifier": "", "timeouts": null, "ttl": 300, "type": "A", "weighted_routing_policy": [], "zone_id": "Z0179074ADDMBNZ4MDD4"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "kubectl_manifest", "name": "ecr_credentials", "provider": "provider[\"registry.terraform.io/alekc/kubectl\"]", "instances": [{"schema_version": 1, "attributes": {"api_version": "secrets.fireflycons.io/v1beta1", "apply_only": false, "delete_cascade": null, "field_manager": "kubectl", "force_conflicts": false, "force_new": false, "id": "/apis/secrets.fireflycons.io/v1beta1/namespaces/datamart-pipeline/ecrsecrets/ecr-secret", "ignore_fields": null, "kind": "ECRSecret", "live_manifest_incluster": "apiVersion=9bf0a493c93c472166ec1673b005088d93994d331ab8b6fd77cd555678fa2f15\nkind=b94f39abc98534b0b175e6d9135e084e51efbbaf404e6e0b8cdcdf6a31b42cec\nmetadata.name=28b44c67ba7917ef7ffbca7a074a21a1045c3f82d19cdcd26f660dbdc136a3af\nmetadata.namespace=05eb82f9527e327c81bc3667ea07cffff5916e88615b24f0712903bbec490667\nspec.registry=62596f15d26ec56a10809961c06e012371ff7129d306c7e14a0f1a77b361e616\nspec.secretName=28b44c67ba7917ef7ffbca7a074a21a1045c3f82d19cdcd26f660dbdc136a3af", "live_uid": "39b789e8-75cc-4662-99c0-d2eee15cda94", "name": "ecr-secret", "namespace": "datamart-pipeline", "override_namespace": null, "sensitive_fields": null, "server_side_apply": false, "timeouts": null, "uid": "39b789e8-75cc-4662-99c0-d2eee15cda94", "validate_schema": true, "wait": null, "wait_for": [], "wait_for_rollout": true, "yaml_body": "apiVersion: secrets.fireflycons.io/v1beta1\nkind: ECRSecret\nmetadata:\n  name: ecr-secret\n  namespace: datamart-pipeline\nspec:\n  registry: ************.dkr.ecr.us-east-1.amazonaws.com\n  secretName: ecr-secret\n", "yaml_body_parsed": "apiVersion: secrets.fireflycons.io/v1beta1\nkind: ECRSecret\nmetadata:\n  name: ecr-secret\n  namespace: datamart-pipeline\nspec:\n  registry: ************.dkr.ecr.us-east-1.amazonaws.com\n  secretName: ecr-secret\n", "yaml_incluster": "apiVersion=9bf0a493c93c472166ec1673b005088d93994d331ab8b6fd77cd555678fa2f15\nkind=b94f39abc98534b0b175e6d9135e084e51efbbaf404e6e0b8cdcdf6a31b42cec\nmetadata.name=28b44c67ba7917ef7ffbca7a074a21a1045c3f82d19cdcd26f660dbdc136a3af\nmetadata.namespace=05eb82f9527e327c81bc3667ea07cffff5916e88615b24f0712903bbec490667\nspec.registry=62596f15d26ec56a10809961c06e012371ff7129d306c7e14a0f1a77b361e616\nspec.secretName=28b44c67ba7917ef7ffbca7a074a21a1045c3f82d19cdcd26f660dbdc136a3af"}, "sensitive_attributes": [[{"type": "get_attr", "value": "yaml_body"}], [{"type": "get_attr", "value": "live_manifest_incluster"}], [{"type": "get_attr", "value": "yaml_incluster"}]], "private": "********************************************************************************************************************", "dependencies": ["kubernetes_namespace.project_namespace"]}]}, {"mode": "managed", "type": "kubectl_manifest", "name": "local_filesystem_pv", "provider": "provider[\"registry.terraform.io/alekc/kubectl\"]", "instances": [{"schema_version": 1, "attributes": {"api_version": "v1", "apply_only": false, "delete_cascade": null, "field_manager": "kubectl", "force_conflicts": false, "force_new": false, "id": "/api/v1/persistentvolumes/datamart-pipeline-pv", "ignore_fields": null, "kind": "PersistentVolume", "live_manifest_incluster": "apiVersion=3bfc269594ef649228e9a74bab00f042efc91d5acc6fbee31a382e80d42388fe\nkind=160ef2faa875a0b9d117c2f9c4305d636a8fd251b87750ff03c82773ad38e452\nmetadata.name=9fe28793bede406be144c07ba2b94d0eb77d4698eca6e5116c2dbe6c403f2a19\nspec.accessModes.#=6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b\nspec.accessModes.0=3440ee021a7b6b876cfd375fa2aaed30e379c78221656dcb7f4d6051544fa57a\nspec.capacity.storage=a6a6a53775e9b613963c915ab0d05efe5f4ff7960ff37194319f3d98eee1727b\nspec.hostPath.path=647745d8796367cccbc72fa87fd73de0ae5378646f7e22e5d641d7d463ffe005\nspec.storageClassName=36bde66f289a35683683b041c6d8f418a5f36607b547da25d00ad55891e80b88", "live_uid": "36ba891a-b555-4501-a1b1-93ee1433f15c", "name": "datamart-pipeline-pv", "namespace": null, "override_namespace": null, "sensitive_fields": null, "server_side_apply": false, "timeouts": null, "uid": "36ba891a-b555-4501-a1b1-93ee1433f15c", "validate_schema": true, "wait": null, "wait_for": [], "wait_for_rollout": true, "yaml_body": "apiVersion: v1\nkind: PersistentVolume\nmetadata:\n  name: \"datamart-pipeline-pv\"\nspec:\n  capacity:\n    storage: 10Gi\n  accessModes:\n    - ReadWriteMany\n  hostPath:\n    path: /opt/datamart-dagster\n  storageClassName: manual\n", "yaml_body_parsed": "apiVersion: v1\nkind: PersistentVolume\nmetadata:\n  name: datamart-pipeline-pv\nspec:\n  accessModes:\n  - ReadWriteMany\n  capacity:\n    storage: 10Gi\n  hostPath:\n    path: /opt/datamart-dagster\n  storageClassName: manual\n", "yaml_incluster": "apiVersion=3bfc269594ef649228e9a74bab00f042efc91d5acc6fbee31a382e80d42388fe\nkind=160ef2faa875a0b9d117c2f9c4305d636a8fd251b87750ff03c82773ad38e452\nmetadata.name=9fe28793bede406be144c07ba2b94d0eb77d4698eca6e5116c2dbe6c403f2a19\nspec.accessModes.#=6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b\nspec.accessModes.0=3440ee021a7b6b876cfd375fa2aaed30e379c78221656dcb7f4d6051544fa57a\nspec.capacity.storage=a6a6a53775e9b613963c915ab0d05efe5f4ff7960ff37194319f3d98eee1727b\nspec.hostPath.path=647745d8796367cccbc72fa87fd73de0ae5378646f7e22e5d641d7d463ffe005\nspec.storageClassName=36bde66f289a35683683b041c6d8f418a5f36607b547da25d00ad55891e80b88"}, "sensitive_attributes": [[{"type": "get_attr", "value": "yaml_incluster"}], [{"type": "get_attr", "value": "live_manifest_incluster"}], [{"type": "get_attr", "value": "yaml_body"}]], "private": "********************************************************************************************************************"}]}, {"mode": "managed", "type": "kubectl_manifest", "name": "local_filesystem_pvc", "provider": "provider[\"registry.terraform.io/alekc/kubectl\"]", "instances": [{"schema_version": 1, "attributes": {"api_version": "v1", "apply_only": false, "delete_cascade": null, "field_manager": "kubectl", "force_conflicts": false, "force_new": false, "id": "/api/v1/namespaces/datamart-pipeline/persistentvolumeclaims/datamart-pipeline-pvc", "ignore_fields": null, "kind": "PersistentVolumeClaim", "live_manifest_incluster": "apiVersion=3bfc269594ef649228e9a74bab00f042efc91d5acc6fbee31a382e80d42388fe\nkind=2209d71260d904304ffe4843e2c7b2141a520f62ff122b557eb9a99001b96639\nmetadata.name=815781a701004254238713c43586da9a5a8571c3976061d74e5349b26d453568\nmetadata.namespace=05eb82f9527e327c81bc3667ea07cffff5916e88615b24f0712903bbec490667\nspec.accessModes.#=6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b\nspec.accessModes.0=3440ee021a7b6b876cfd375fa2aaed30e379c78221656dcb7f4d6051544fa57a\nspec.resources.requests.storage=a6a6a53775e9b613963c915ab0d05efe5f4ff7960ff37194319f3d98eee1727b\nspec.storageClassName=36bde66f289a35683683b041c6d8f418a5f36607b547da25d00ad55891e80b88", "live_uid": "c48396ba-aa87-4b8c-8567-00b601104397", "name": "datamart-pipeline-pvc", "namespace": "datamart-pipeline", "override_namespace": null, "sensitive_fields": null, "server_side_apply": false, "timeouts": null, "uid": "c48396ba-aa87-4b8c-8567-00b601104397", "validate_schema": true, "wait": null, "wait_for": [], "wait_for_rollout": true, "yaml_body": "apiVersion: v1\nkind: PersistentVolumeClaim\nmetadata:\n  name: \"datamart-pipeline-pvc\"\n  namespace: datamart-pipeline\nspec:\n  accessModes:\n   - ReadWriteMany\n  resources:\n    requests:\n      storage: 10Gi\n  storageClassName: manual\n", "yaml_body_parsed": "apiVersion: v1\nkind: PersistentVolumeClaim\nmetadata:\n  name: datamart-pipeline-pvc\n  namespace: datamart-pipeline\nspec:\n  accessModes:\n  - ReadWriteMany\n  resources:\n    requests:\n      storage: 10Gi\n  storageClassName: manual\n", "yaml_incluster": "apiVersion=3bfc269594ef649228e9a74bab00f042efc91d5acc6fbee31a382e80d42388fe\nkind=2209d71260d904304ffe4843e2c7b2141a520f62ff122b557eb9a99001b96639\nmetadata.name=815781a701004254238713c43586da9a5a8571c3976061d74e5349b26d453568\nmetadata.namespace=05eb82f9527e327c81bc3667ea07cffff5916e88615b24f0712903bbec490667\nspec.accessModes.#=6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b\nspec.accessModes.0=3440ee021a7b6b876cfd375fa2aaed30e379c78221656dcb7f4d6051544fa57a\nspec.resources.requests.storage=a6a6a53775e9b613963c915ab0d05efe5f4ff7960ff37194319f3d98eee1727b\nspec.storageClassName=36bde66f289a35683683b041c6d8f418a5f36607b547da25d00ad55891e80b88"}, "sensitive_attributes": [[{"type": "get_attr", "value": "live_manifest_incluster"}], [{"type": "get_attr", "value": "yaml_body"}], [{"type": "get_attr", "value": "yaml_incluster"}]], "private": "********************************************************************************************************************", "dependencies": ["kubernetes_namespace.project_namespace"]}]}, {"mode": "managed", "type": "kubectl_manifest", "name": "project_webui_service", "provider": "provider[\"registry.terraform.io/alekc/kubectl\"]", "instances": [{"schema_version": 1, "attributes": {"api_version": "v1", "apply_only": false, "delete_cascade": null, "field_manager": "kubectl", "force_conflicts": false, "force_new": false, "id": "/api/v1/namespaces/datamart-pipeline/services/datamart-dagster-pipeline-service", "ignore_fields": null, "kind": "Service", "live_manifest_incluster": "apiVersion=3bfc269594ef649228e9a74bab00f042efc91d5acc6fbee31a382e80d42388fe\nkind=d677190e0a9990e7d5fa9e4c1bbde44271fb8959c4acb6d43e02ed991128b4bf\nmetadata.name=b6a469bd050312a3497516972251c5baed35438f53b0322507f2408f0ef905a5\nmetadata.namespace=05eb82f9527e327c81bc3667ea07cffff5916e88615b24f0712903bbec490667\nspec.ports.#=6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b\nspec.ports.0.port=a176eeb31e601c3877c87c2843a2f584968975269e369d5c86788b4c2f92d2a2\nspec.ports.0.protocol=2e9430507b92dee11e1a03bc534f67001d8347f4e61294e45739515670af6467\nspec.ports.0.targetPort=a176eeb31e601c3877c87c2843a2f584968975269e369d5c86788b4c2f92d2a2\nspec.selector.app=e7364e634bc8e70ea3d7040ef1ffc820aa20c06a0d0b3e2b4cc543fa04cb0034\nspec.type=d6e79bcb9ebe2da8c86ef1939289fc28e7c5eccd72d5dde3ec3f618e06c13218", "live_uid": "09bd1e58-a9eb-4756-bfdf-552645d9ec86", "name": "datamart-dagster-pipeline-service", "namespace": "datamart-pipeline", "override_namespace": null, "sensitive_fields": null, "server_side_apply": false, "timeouts": null, "uid": "09bd1e58-a9eb-4756-bfdf-552645d9ec86", "validate_schema": true, "wait": null, "wait_for": [], "wait_for_rollout": true, "yaml_body": "apiVersion: v1\nkind: Service\nmetadata:\n  name: \"datamart-dagster-pipeline-service\"\n  namespace: \"datamart-pipeline\"\nspec:\n  selector:\n    app: \"datamart-dagster-pipeline\"\n  ports:\n    - port: 3000\n      targetPort: 3000\n      protocol: TCP\n  type: ClusterIP\n", "yaml_body_parsed": "apiVersion: v1\nkind: Service\nmetadata:\n  name: datamart-dagster-pipeline-service\n  namespace: datamart-pipeline\nspec:\n  ports:\n  - port: 3000\n    protocol: TCP\n    targetPort: 3000\n  selector:\n    app: datamart-dagster-pipeline\n  type: ClusterIP\n", "yaml_incluster": "apiVersion=3bfc269594ef649228e9a74bab00f042efc91d5acc6fbee31a382e80d42388fe\nkind=d677190e0a9990e7d5fa9e4c1bbde44271fb8959c4acb6d43e02ed991128b4bf\nmetadata.name=b6a469bd050312a3497516972251c5baed35438f53b0322507f2408f0ef905a5\nmetadata.namespace=05eb82f9527e327c81bc3667ea07cffff5916e88615b24f0712903bbec490667\nspec.ports.#=6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b\nspec.ports.0.port=a176eeb31e601c3877c87c2843a2f584968975269e369d5c86788b4c2f92d2a2\nspec.ports.0.protocol=2e9430507b92dee11e1a03bc534f67001d8347f4e61294e45739515670af6467\nspec.ports.0.targetPort=a176eeb31e601c3877c87c2843a2f584968975269e369d5c86788b4c2f92d2a2\nspec.selector.app=e7364e634bc8e70ea3d7040ef1ffc820aa20c06a0d0b3e2b4cc543fa04cb0034\nspec.type=d6e79bcb9ebe2da8c86ef1939289fc28e7c5eccd72d5dde3ec3f618e06c13218"}, "sensitive_attributes": [[{"type": "get_attr", "value": "yaml_body"}], [{"type": "get_attr", "value": "yaml_incluster"}], [{"type": "get_attr", "value": "live_manifest_incluster"}]], "private": "********************************************************************************************************************", "dependencies": ["kubernetes_namespace.project_namespace"]}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "project_namespace", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "datamart-pipeline", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "datamart-pipeline", "resource_version": "3123619", "uid": "13c5658a-0295-474a-8111-71d33c728ca1"}], "timeouts": null, "wait_for_default_service_account": false}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_pod_v1", "name": "project_deployment", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "datamart-pipeline/datamart-dagster-pipeline", "metadata": [{"annotations": null, "generate_name": "", "generation": 0, "labels": {"app": "datamart-dagster-pipeline"}, "name": "datamart-dagster-pipeline", "namespace": "datamart-pipeline", "resource_version": "3123676", "uid": "48eec342-75a9-4e3f-891f-1c3db2df1c33"}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": true, "container": [{"args": ["dbt compile --project-dir doris_materialization/dbt --profiles-dir doris_materialization/dbt ; dagster dev -h 0.0.0.0 -p 3000"], "command": ["/bin/bash", "-c"], "env": [], "env_from": [{"config_map_ref": [], "prefix": "", "secret_ref": [{"name": "project-env-config", "optional": false}]}], "image": "************.dkr.ecr.us-east-1.amazonaws.com/atlas-dagster-pipelines-datamart:**********", "image_pull_policy": "IfNotPresent", "lifecycle": [], "liveness_probe": [], "name": "datamart-dagster-pipeline", "port": [], "readiness_probe": [], "resources": [{"limits": {}, "requests": {}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [{"mount_path": "/opt/dagster/dagster_home/", "mount_propagation": "None", "name": "project-home", "read_only": false, "sub_path": ""}], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": true, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [{"name": "ecr-secret"}], "init_container": [], "node_name": "atlas-k3", "node_selector": null, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "default-scheduler", "security_context": [], "service_account_name": "default", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": [{"aws_elastic_block_store": [], "azure_disk": [], "azure_file": [], "ceph_fs": [], "cinder": [], "config_map": [], "csi": [], "downward_api": [], "empty_dir": [], "ephemeral": [], "fc": [], "flex_volume": [], "flocker": [], "gce_persistent_disk": [], "git_repo": [], "glusterfs": [], "host_path": [], "iscsi": [], "local": [], "name": "project-home", "nfs": [], "persistent_volume_claim": [{"claim_name": "datamart-pipeline-pvc", "read_only": false}], "photon_persistent_disk": [], "projected": [], "quobyte": [], "rbd": [], "secret": [], "vsphere_volume": []}]}], "target_state": null, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["kubectl_manifest.ecr_credentials", "kubectl_manifest.local_filesystem_pvc", "kubernetes_namespace.project_namespace", "kubernetes_secret_v1.project_env_config"]}]}, {"mode": "managed", "type": "kubernetes_secret_v1", "name": "project_env_config", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"binary_data": null, "binary_data_wo": null, "binary_data_wo_revision": null, "data": {}, "data_wo": null, "data_wo_revision": null, "id": "datamart-pipeline/project-env-config", "immutable": false, "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "project-env-config", "namespace": "datamart-pipeline", "resource_version": "1768414", "uid": "221310aa-3aa8-4c8b-9720-105a8838560f"}], "timeouts": null, "type": "Opaque", "wait_for_service_account_token": true}, "sensitive_attributes": [[{"type": "get_attr", "value": "data"}], [{"type": "get_attr", "value": "binary_data"}]], "private": "************************************************************************************", "dependencies": ["kubernetes_namespace.project_namespace"]}]}, {"module": "module.project_ingress", "mode": "managed", "type": "helm_release", "name": "ingress", "provider": "module.project_ingress.provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"schema_version": 1, "attributes": {"atomic": false, "chart": "../_helpers/traefik-ingress-helm-********/module/yaml-wrapper", "cleanup_on_fail": true, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "datamart-dagster-pipeline-service-ingress", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "1.16.0", "chart": "yaml-wrapper", "first_deployed": **********, "last_deployed": **********, "name": "datamart-dagster-pipeline-service-ingress", "namespace": "datamart-pipeline", "notes": "", "revision": 1, "values": "{\"certmanager_issuer\":\"letsencrypt-prod\",\"credential_name\":\"cert-dagster-datamart.atlas.local.stl.waschick.org\",\"instance_name\":\"datamart-dagster-pipeline-service\",\"service_port\":3000,\"site_fqdn\":\"dagster-datamart.atlas.local.stl.waschick.org\"}", "version": "0.1.0"}], "name": "datamart-dagster-pipeline-service-ingress", "namespace": "datamart-pipeline", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": null, "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [{"name": "certmanager_issuer", "type": "", "value": "letsencrypt-prod"}, {"name": "credential_name", "type": "", "value": "cert-dagster-datamart.atlas.local.stl.waschick.org"}, {"name": "instance_name", "type": "", "value": "datamart-dagster-pipeline-service"}, {"name": "service_port", "type": "", "value": "3000"}, {"name": "site_fqdn", "type": "", "value": "dagster-datamart.atlas.local.stl.waschick.org"}], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "deployed", "timeout": 300, "upgrade_install": null, "values": null, "verify": false, "version": "0.1.0", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "repository_password"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["kubectl_manifest.project_webui_service", "kubernetes_namespace.project_namespace"]}]}], "check_results": null}