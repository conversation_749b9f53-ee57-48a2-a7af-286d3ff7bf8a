# Define paths to the scripts managing makefile actions
s_build_local_image = ./script/build_local_image.sh
s_build_local_image_no_cache = ./script/build_local_image_no_cache.sh
s_build_prod_image = ./script/build_prod_image.sh
s_docker_auth = ./script/docker_auth.sh
s_push_prod_image = ./script/push_prod_image.sh
s_run_container = ./script/run_container.sh
s_run_container_foreground = ./script/run_container_foreground.sh
s_show_containers = ./script/show_containers.sh
s_stop_container = ./script/stop_container.sh
s_connect_background_container = ./script/connect_background_container.sh
s_init_terraform = ./script/init_terraform.sh
s_terraform_start = ./script/terraform_start.sh
s_terraform_stop = ./script/terraform_stop.sh
s_terraform_plan = ./script/terraform_plan.sh
s_k8s_status = ./script/k8s_status.sh
s_k8s_project_shell = ./script/k8s_project_shell.sh
s_choose_cluster = ./script/choose_cluster.sh
s_check_selection = ./script/check_selection.sh

.PHONY: tf-start tf-stop tf-plan

.ONESHELL:
	SHELL = /bin/bash

define run
	@chmod +x $1
	@$1 $2
endef

all:
	@echo "\033[1mCONTAINER BUILD, DEPLOY, AND MANAGEMNENT\033[0m"
	@echo ""
	@echo "This makefile will trigger scripts in the local \033[3m/infrastructure/script\033[0m directory, as defined by values in the \033[3m/infrastructure/config\033[0m directory, to allow you to build, execute & test locally, and deploy this project as a production containerized workload."
	@echo ""
	@echo "These scripts will use the same Dockerfile used for development in the root of the project, but will build it with options defined in the infrastructure config. This should give you decent flexibility in local versus production environments, while still maintaining consistency in internal package selection and configuration."
	@echo ""
	@echo "\033[1mContainer Building Commands:\033[0m"
	@echo "  make build  - Build the container for local infrastructure, no caching of layers"
	@echo "  make build-quick  - Build the container for local infrastructure, with caching"
	@echo "  make build-prod  - Build the container for production infrastructure"
	@echo "  make push-prod  - Push the last-built production container image to the designated container registry"
	@echo "  make build-push-prod  - Build the container for production infrastructure and immediately push it to the registry"
	@echo "  make docker-auth  - Sign into the designated container registry"
	@echo ""
	@echo "\033[1mContainer Testing/Execution Commands:\033[0m"
	@echo "  make start  - Run the container for local infrastructure in the background, building first if necessary"
	@echo "  make stop  - Terminate the instance of the container running in the background"
	@echo "  make connect  - Open a console to the instance of the container running in the background"
	@echo "  make start-foreground  - Run the container for local infrastructure in the foreground, building first if necessary"
	@echo ""
	@echo "\033[1mDeclarative Infrastructure Commands:\033[0m"
	@echo "  make k8s-select  - Choose which cluster configuration to work with. Defaults to \033[3mlocal\033[0m"
	@echo "  make k8s-status  - Get the current state of the selected Kubernetes cluster"
	@echo "  make k8s-shell  - Open a shell to the currently running instance of the main project workload"
	@echo "  make tf-init  - Install Terraform executable into a local .venv in this directory"
	@echo "  make tf-start {plan}  - Deploy one of the terraform plans found in the \033[3m/infrastructure/terraform\033[0m directory"
	@echo "  make tf-stop {plan}  - Terminate one of the terraform plans on the target system"
	@echo "  make tf-plan {plan}  - Review target state, show proposed deployment steps for a given plan"

build:
	$(call run, $(s_build_local_image_no_cache) )

build-quick:
	$(call run, $(s_build_local_image) )

build-prod:
	$(call run, $(s_build_prod_image) )

push-prod:
	$(call run, $(s_docker_auth) )
	$(call run, $(s_push_prod_image) )

build-push-prod:
	$(call run, $(s_docker_auth) )
	$(call run, $(s_build_prod_image) )
	$(call run, $(s_push_prod_image) )

start:
	$(call run, $(s_run_container) )
	$(call run, $(s_show_containers) )

start-foreground:
	$(call run, $(s_run_container_foreground) )

stop:
	$(call run, $(s_stop_container) )

connect:
	$(call run, $(s_connect_background_container) )

docker-auth:
	$(call run, $(s_docker_auth) )

tf-init:
	$(call run, $(s_check_selection) )
	$(call run, $(s_init_terraform) )

tf-start:
	$(call run, $(s_check_selection) )
	$(call run, $(s_terraform_start),$(filter-out $@,$(MAKECMDGOALS)))

tf-stop:
	$(call run, $(s_check_selection) )
	$(call run, $(s_terraform_stop),$(filter-out $@,$(MAKECMDGOALS)))

tf-plan:
	$(call run, $(s_check_selection) )
	$(call run, $(s_terraform_plan),$(filter-out $@,$(MAKECMDGOALS)))

k8s-status:
	$(call run, $(s_check_selection) )
	$(call run, $(s_k8s_status) )

k8s-select:
	$(call run, $(s_choose_cluster) )

k8s-shell:
	$(call run, $(s_check_selection) )
	$(call run, $(s_k8s_project_shell) )

%:
	@true
