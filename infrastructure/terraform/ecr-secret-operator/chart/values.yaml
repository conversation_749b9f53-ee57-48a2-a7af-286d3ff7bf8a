# Generated by [<PERSON><PERSON>helmize](https://github.com/yeahdongcn/kustohelmize)
AWS:
  "*************":
    accessKey: AKAIEXAMPLE
    secretKey: dskwr4EXAMPLE
prometheus:
  enabled: false
serviceAccount:
  create: true
ecrSecretOperatorControllerManagerDeployment:
  manager:
    args:
      - --health-probe-bind-address=:8081
      - --metrics-bind-address=127.0.0.1:8080
      - --leader-elect
      - --config-file=/etc/manager-config/config.toml
    image:
      repository: fireflycons/ecr-secret-operator
  replicas: 1
