{{- if .Values.prometheus.enabled }}
# Generated by [Kustohelmize](https://github.com/yeahdongcn/kustohelmize)
kind: ServiceMonitor
metadata: 
  labels:
    {{- include "ecr-secret-operator.labels" . | nindent 4 }}
  name: ecr-secret-operator-controller-manager-metrics-monitor
spec: 
  endpoints:     
    - tlsConfig: 
        insecureSkipVerify: true
      bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
      path: /metrics
      port: https
      scheme: https
  selector: 
    matchLabels: 
      control-plane: {{ include "ecr-secret-operator.contollerManagerLabel" . }}
apiVersion: monitoring.coreos.com/v1
{{- end}}
