# Generated by [<PERSON><PERSON>helmize](https://github.com/yeahdongcn/kustohelmize)
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata: 
  labels:
    {{- include "ecr-secret-operator.labels" . | nindent 4 }}
  name: ecr-secret-operator-proxy-role
rules:   
  - resources: 
      - tokenreviews
    verbs: 
      - create
    apiGroups: 
      - authentication.k8s.io
  - apiGroups: 
      - authorization.k8s.io
    resources: 
      - subjectaccessreviews
    verbs: 
      - create
