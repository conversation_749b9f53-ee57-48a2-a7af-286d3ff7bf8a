# Generated by [Ku<PERSON>helmize](https://github.com/yeahdongcn/kustohelmize)
metadata: 
  labels:
    {{- include "ecr-secret-operator.labels" . | nindent 4 }}
  name: ecr-secret-operator-proxy-rolebinding
roleRef: 
  name: ecr-secret-operator-proxy-role
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
subjects:   
  - namespace: {{ .Release.Namespace }}
    kind: ServiceAccount
    name: {{ include "ecr-secret-operator.serviceAccountName" . }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
