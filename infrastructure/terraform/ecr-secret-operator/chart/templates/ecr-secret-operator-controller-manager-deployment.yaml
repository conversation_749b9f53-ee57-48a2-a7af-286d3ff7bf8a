# Generated by [Ku<PERSON>helmize](https://github.com/yeahdongcn/kustohelmize)
kind: Deployment
metadata: 
  labels:
    {{- include "ecr-secret-operator.labels" . | nindent 4 }}
  name: ecr-secret-operator-controller-manager
spec: 
  replicas: {{ .Values.ecrSecretOperatorControllerManagerDeployment.replicas }}
  selector: 
    matchLabels: 
      control-plane: {{ include "ecr-secret-operator.contollerManagerLabel" . }}
  template: 
    metadata: 
      annotations: 
        kubectl.kubernetes.io/default-container: manager
      labels: 
        control-plane: {{ include "ecr-secret-operator.contollerManagerLabel" . }}
    spec: 
      affinity: 
        nodeAffinity: 
          requiredDuringSchedulingIgnoredDuringExecution: 
            nodeSelectorTerms:               
              - matchExpressions:                   
                  - key: kubernetes.io/arch
                    operator: In
                    values: 
                      - amd64
                      - arm64
                      - ppc64le
                      - s390x
                  - operator: In
                    values: 
                      - linux
                    key: kubernetes.io/os
      containers:         
        - name: kube-rbac-proxy
          ports:             
            - containerPort: 8443
              name: https
              protocol: TCP
          resources: 
            limits: 
              cpu: 500m
              memory: 128Mi
            requests: 
              cpu: 5m
              memory: 64Mi
          securityContext: 
            allowPrivilegeEscalation: false
            capabilities: 
              drop: 
                - ALL
          args: 
            - --secure-listen-address=0.0.0.0:8443
            - --upstream=http://127.0.0.1:8080/
            - --logtostderr=true
            - --v=0
          image: gcr.io/kubebuilder/kube-rbac-proxy:v0.13.1
        - resources: 
            requests: 
              cpu: 10m
              memory: 64Mi
            limits: 
              cpu: 500m
              memory: 128Mi
          securityContext: 
            capabilities: 
              drop: 
                - ALL
            allowPrivilegeEscalation: false
          volumeMounts:             
            - mountPath: /etc/manager-config
              name: config
              readOnly: true
          {{- with .Values.ecrSecretOperatorControllerManagerDeployment.manager.args }}
          args:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          image: "{{ .Values.ecrSecretOperatorControllerManagerDeployment.manager.image.repository }}:{{ include "ecr-secret-operator.imageTag" . }}"
          livenessProbe: 
            httpGet: 
              path: /healthz
              port: 8081
            initialDelaySeconds: 15
            periodSeconds: 20
          command: 
            - /manager
          name: manager
          readinessProbe: 
            httpGet: 
              path: /readyz
              port: 8081
            initialDelaySeconds: 5
            periodSeconds: 10
      securityContext: 
        runAsNonRoot: true
      serviceAccountName: {{ include "ecr-secret-operator.serviceAccountName" . }}
      terminationGracePeriodSeconds: 10
      volumes:         
        - name: config
          secret: 
            secretName: {{ include "ecr-secret-operator.secretName" . }}
            optional: false
apiVersion: apps/v1
