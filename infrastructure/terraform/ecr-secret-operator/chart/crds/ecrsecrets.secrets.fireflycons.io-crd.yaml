apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.1
  creationTimestamp: null
  name: ecrsecrets.secrets.fireflycons.io
spec:
  group: secrets.fireflycons.io
  names:
    kind: ECRSecret
    listKind: ECRSecretList
    plural: ecrsecrets
    singular: ecrsecret
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: ECRSecret is the Schema for the ecrsecrets API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ECRSecretSpec defines the desired state of ECRSecret
            properties:
              registry:
                pattern: ^\d{12}\.dkr.ecr.(ap|ca|eu|sa|us(-gov)?)-(east|northeast|southeast|north|south|southeast|central|west)-\d\.amazonaws\.com$
                type: string
              secretName:
                pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                type: string
            type: object
          status:
            description: ECRSecretStatus defines the observed state of ECRSecret
            properties:
              lastUpdated:
                format: date-time
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
