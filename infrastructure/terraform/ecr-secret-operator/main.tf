terraform {
    required_version = ">= 1.5"
    backend "local" {
        path = "../_state/ecr-secret-operator"
    }
    required_providers {
        helm = {
            source = "hashicorp/helm"
        }
    }
}

# set variables prefixed with `local.`
# These are defined by files in `config/{package}` and overridden by `_clusters/{selected cluster}/{package}
# variables defined in `options` are used with the format: local.config.{package}_{option}, and will contain
# the contents of the file in that config directory.

locals {
    cluster = file("../../config/_clusters/selection")
    config =  jsondecode(templatefile("../_helpers/config.tmpl", {
        options = [
            { package="ecr-secret-operator", option="kubernetes-namespace" },
            { package="aws", option="account-id" },
            { package="aws", option="access-key" },
            { package="aws", option="access-secret" },
        ]
        cluster_selection = local.cluster
        cluster_path = "../../config/_clusters/${local.cluster}"
        common_path = "../../config/"
        }
    ))
}

variable "kubeconfig" { type=string }

provider "helm" {
    kubernetes {
        config_path = var.kubeconfig
    }
}

# https://github.com/fireflycons/ecr-secret-operator/tree/master
resource "helm_release" "ecr_secret_operator" {
    name       = "ecr-secret-operator"
    chart      = "${path.module}/chart"
    namespace  = local.config.ecr-secret-operator_kubernetes-namespace
    create_namespace = true
    values = [
    <<EOT
AWS:
  "${local.config.aws_account-id}":
    accessKey: ${local.config.aws_access-key}
    secretKey: ${local.config.aws_access-secret}
EOT
    ]
}
