terraform {
    required_version = ">= 1.5"
    backend "local" {
        path = "../_state/cert-manager"
    }  
    required_providers {
        helm = {
            source = "hashicorp/helm"
        }
    }
}

# set variables prefixed with `local.` 
# These are defined by files in `config/{package}` and overridden by `_clusters/{selected cluster}/{package}
# variables defined in `options` are used with the format: local.config.{package}_{option}, and will contain 
# the contents of the file in that config directory. 

locals {
    cluster = file("../../config/_clusters/selection")
    config =  jsondecode(templatefile("../_helpers/config.tmpl", {
        options = [
            { package="cert-manager", option="kubernetes-namespace" },
        ]
        cluster_selection = local.cluster
        cluster_path = "../../config/_clusters/${local.cluster}"
        common_path = "../../config/"
        }
    ))
}

# this is set to /opt/kubeconfigs/default, which is a symlink to whichever cluster's kubeconfig is currently selected
variable "kubeconfig" { type=string }

provider "helm" {
    kubernetes {
        config_path = var.kubeconfig
    }
}

resource "helm_release" "cert-manager" {
    name = "cert-manager"
    repository = "https://charts.jetstack.io"
    chart = "cert-manager"
    namespace = local.config.cert-manager_kubernetes-namespace
    create_namespace = true
    cleanup_on_fail = true
    set {
        name = "installCRDs"
        value = true
    }
}
