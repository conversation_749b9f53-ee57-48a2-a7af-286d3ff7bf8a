terraform {
    required_version = ">= 1.5"
    backend "local" {
        path = "../_state/nfs-pv-provisioner"
    }  
    required_providers {
        helm = {
            source = "hashicorp/helm"
        }
    }
}

# set variables prefixed with `local.` 
# These are defined by files in `config/{package}` and overridden by `_clusters/{selected cluster}/{package}
# variables defined in `options` are used with the format: local.config.{package}_{option}, and will contain 
# the contents of the file in that config directory. 

locals {
    cluster = file("../../config/_clusters/selection")
    config =  jsondecode(templatefile("../_helpers/config.tmpl", {
        options = [
            { package="nfs-pv-provisioner", option="nfs-host" },
            { package="nfs-pv-provisioner", option="nfs-path" },
            { package="nfs-pv-provisioner", option="kubernetes-namespace" },
            { package="nfs-pv-provisioner", option="is-default-csi" },
        ]
        cluster_selection = local.cluster
        cluster_path = "../../config/_clusters/${local.cluster}"
        common_path = "../../config/"
        }
    ))
}

# this is set to /opt/kubeconfigs/default, which is a symlink to whichever cluster's kubeconfig is currently selected
variable "kubeconfig" { type=string }

provider "helm" {
    kubernetes {
        config_path = var.kubeconfig
    }
}

resource "helm_release" "nfs-pv-provisioner" {
    name = "nfs-pv-provisioner"
    repository = "https://kubernetes-sigs.github.io/nfs-subdir-external-provisioner/"
    chart = "nfs-subdir-external-provisioner"
    namespace = local.config.nfs-pv-provisioner_kubernetes-namespace
    create_namespace = true

    set {
        name  = "nfs.server"
        value = local.config.nfs-pv-provisioner_nfs-host
    }

    set {
        name  = "nfs.path"
        value = local.config.nfs-pv-provisioner_nfs-path
    } 

    set {
        name  = "storageClass.defaultClass"
        value = local.config.nfs-pv-provisioner_is-default-csi
    }    
}
