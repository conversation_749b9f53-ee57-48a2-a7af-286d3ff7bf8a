terraform {
    required_version = ">= 1.5"
    backend "local" {
        path = "../_state/project"
    }
    required_providers {
        helm = {
            source = "hashicorp/helm"
        }
        kubernetes = {
            source = "hashicorp/kubernetes"
        }
        kubectl = {
            source  = "alekc/kubectl"
        }
        aws = {
            source = "hashicorp/aws"
        }
    }
}

# set variables prefixed with `local.`
# These are defined by files in `config/{package}` and overridden by `_clusters/{selected cluster}/{package}
# variables defined in `options` are used with the format: local.config.{package}_{option}, and will contain
# the contents of the file in that config directory.

locals {
    cluster = file("../../config/_clusters/selection")
    config =  jsondecode(templatefile("../_helpers/config.tmpl", {
        options = [
            { package="project", option="kubernetes-namespace" },
            { package="project", option="persistent-volume-name" },
            { package="project", option="persistent-volume-host-path" },
            { package="project", option="persistent-volume-target-path" },
            { package="project", option="app-deployment-name" },
            { package="project", option="env-file-name" },
            { package="project", option="container-http-port" },
            { package="project", option="container-entrypoint-command" },
            { package="project", option="app-hostname" },
            { package="docker", option="registry" },
            { package="docker", option="repository" },
            { package="docker", option="tag" },
            { package="cluster", option="site-domain" },
            { package="cluster", option="domain-aws-id" },
            { package="cluster", option="ingress-default-address" },
            { package="aws", option="region" },
            { package="aws", option="access-key" },
            { package="aws", option="access-secret" },
            { package="cert-manager", option="cluster-issuer-name" },
        ]
        cluster_selection = local.cluster
        cluster_path = "../../config/_clusters/${local.cluster}"
        common_path = "../../config/"
        }
    ))
}

variable "kubeconfig" { type=string }

provider "kubernetes" {
  config_path    = var.kubeconfig
}

provider "helm" {
    kubernetes {
        config_path = var.kubeconfig
    }
}

provider "kubectl" {
    config_path    = var.kubeconfig
}

provider "aws" {
    region     = local.config.aws_region
    access_key = local.config.aws_access-key
    secret_key = local.config.aws_access-secret
}

resource "kubernetes_namespace" "project_namespace" {
    metadata {
        name = local.config.project_kubernetes-namespace
    }
}

resource "kubectl_manifest" "ecr_credentials" {
    yaml_body = <<YAML
apiVersion: secrets.fireflycons.io/v1beta1
kind: ECRSecret
metadata:
  name: ecr-secret
  namespace: ${kubernetes_namespace.project_namespace.metadata.0.name}
spec:
  registry: ${local.config.docker_registry}
  secretName: ecr-secret
YAML
}

resource "kubectl_manifest" "local_filesystem_pv" {
    yaml_body = <<YAML
apiVersion: v1
kind: PersistentVolume
metadata:
  name: "${local.config.project_persistent-volume-name}-pv"
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  hostPath:
    path: ${local.config.project_persistent-volume-host-path}
  storageClassName: manual
YAML
}

resource "kubectl_manifest" "local_filesystem_pvc" {
    yaml_body = <<YAML
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: "${local.config.project_persistent-volume-name}-pvc"
  namespace: ${kubernetes_namespace.project_namespace.metadata.0.name}
spec:
  accessModes:
   - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: manual
YAML
}

resource "kubernetes_secret_v1" "project_env_config" {
    metadata {
        name = "project-env-config"
        namespace = kubernetes_namespace.project_namespace.metadata.0.name
    }
    data = tomap(
        {
            for line in split("\n", file("../../../${local.config.project_env-file-name}")) :
                trimspace(element(split("=", line), 0)) =>
                trimspace(element(split("=", line), 1))
                if length(line) > 0 && substr(line, 0, 1) != "#" && strcontains(line, "=")
        }
    )
}

resource "kubernetes_pod_v1" "project_deployment" {
    metadata {
        name      = local.config.project_app-deployment-name
        namespace = kubernetes_namespace.project_namespace.metadata.0.name
        labels = {
            app = local.config.project_app-deployment-name
        }
    }
    spec {
        image_pull_secrets {
            name = kubectl_manifest.ecr_credentials.name
        }
        container {
            name  = local.config.project_app-deployment-name
            image = "${local.config.docker_registry}/${local.config.docker_repository}:${local.config.docker_tag}"
            command = ["/bin/bash", "-c"]
            args = ["${local.config.project_container-entrypoint-command}"]
            env_from {
                secret_ref {
                    name = kubernetes_secret_v1.project_env_config.metadata.0.name
                }
            }
            # volume mounts will generally be customized within each deployment specification
            volume_mount {
                name       = "project-home"
                mount_path = "${local.config.project_persistent-volume-target-path}"
                sub_path   = ""
            }
        }
        volume {
            name = "project-home"
            persistent_volume_claim {
                claim_name = kubectl_manifest.local_filesystem_pvc.name
            }
        }
    }
}

resource "kubectl_manifest" "project_webui_service" {
    yaml_body = <<YAML
apiVersion: v1
kind: Service
metadata:
  name: "${local.config.project_app-deployment-name}-service"
  namespace: "${kubernetes_namespace.project_namespace.metadata.0.name}"
spec:
  selector:
    app: "${local.config.project_app-deployment-name}"
  ports:
    - port: ${local.config.project_container-http-port}
      targetPort: ${local.config.project_container-http-port}
      protocol: TCP
  type: ClusterIP
YAML
}

module "project_ingress" {
    source = "../_helpers/traefik-ingress-helm-20250417/module"
    kubeconfig = var.kubeconfig
    kubernetes_namespace = kubernetes_namespace.project_namespace.metadata.0.name
    instance_name = kubectl_manifest.project_webui_service.name
    site_fqdn = "${local.config.project_app-hostname}.${local.config.cluster_site-domain}"
    credential_name = "cert-${local.config.project_app-hostname}.${local.config.cluster_site-domain}"
    certmanager_issuer = local.config.cert-manager_cluster-issuer-name
    service_port = local.config.project_container-http-port
}

resource "aws_route53_record" "project_admin_dns_record" {
    zone_id = local.config.cluster_domain-aws-id
    name    = "${local.config.project_app-hostname}.${local.config.cluster_site-domain}"
    type    = "A"
    ttl     = 300
    records = [local.config.cluster_ingress-default-address]
}

