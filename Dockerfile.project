FROM python:3.12.9-bookworm

ENV DAGSTER_HOME=/opt/dagster/dagster_home/
RUN mkdir -p $DAGSTER_HOME
RUN mkdir -p $DAGSTER_HOME/temp_data
WORKDIR $DAGSTER_HOME

COPY requirements.txt $DAGSTER_HOME
COPY workspace.yaml $DAGSTER_HOME
COPY dagster.yaml $DAGSTER_HOME
COPY doris_materialization $DAGSTER_HOME/doris_materialization

RUN python -m pip install --upgrade pip setuptools wheel
COPY offline_packages $DAGSTER_HOME/offline_packages
RUN pip install -r requirements.txt
